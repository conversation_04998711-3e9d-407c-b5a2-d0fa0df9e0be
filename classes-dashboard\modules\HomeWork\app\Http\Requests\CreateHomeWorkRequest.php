<?php

namespace HomeWork\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateHomeWorkRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'classroom_id' => 'required|string|max:200',
            'subject_id' => 'required|string|max:200',
            'homework_date' => 'required|string|max:200',
            'title' => 'required|string|max:200',
            'description' => 'required|string|max:4096',
        ];
    }
}
