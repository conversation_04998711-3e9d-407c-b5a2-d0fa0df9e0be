<?php

namespace App\Http\APIControllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\StudentFeePaymentRepository;
use Timetable\Repositories\MasterTimetableRepository;
use StudentAttendance\Repositories\AttendanceRepository;
use ClassWork\Repositories\ClassWorkRepository;
use HomeWork\Repositories\HomeWorkRepository;

class StudentAPIController extends Controller
{
    protected $admissionRepository;
    protected $studentFeePaymentRepository;
    protected $masterTimetableRepository;
    protected $attendanceRepository;
    protected $classWorkRepository;
    protected $homeWorkRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        StudentFeePaymentRepository $studentFeePaymentRepository,
        MasterTimetableRepository $masterTimetableRepository,
        AttendanceRepository $attendanceRepository,
        ClassWorkRepository $classWorkRepository,
        HomeWorkRepository $homeWorkRepository
    ) {
        $this->admissionRepository = $admissionRepository;
        $this->studentFeePaymentRepository = $studentFeePaymentRepository;
        $this->masterTimetableRepository = $masterTimetableRepository;
        $this->attendanceRepository = $attendanceRepository;
        $this->classWorkRepository = $classWorkRepository;
        $this->homeWorkRepository = $homeWorkRepository;
    }

    public function getStudentDetails(Request $request)
    {
        $student = $request->get('student');
        return response()->json(apiResonse("Student Details Fetched Successfully", $student));
    }

    public function logout(Request $request)
    {
        return response()->json(apiResonse("Successfully logged out", ''));
    }

    public function getTimetable(Request $request)
    {
        $request->validate(['date' => 'required|date']);

        $academicInfo = $request->get('academic_info');
        $date = $request->get('date');

        $timetable = $this->masterTimetableRepository
            ->getTimetableByClassroom($academicInfo->classroom, $date);

        return response()->json(apiResonse("Timetable Fetched Successfully", $timetable));
    }

    public function getAttendance(Request $request)
    {
        $studentId = $request->get('academic_info')->id;
        $filters = $request->all();

        $attendance = $this->attendanceRepository
            ->getAttendanceForAPI($filters, $studentId);

        return response()->json(apiResonse("Attendance Fetched Successfully", $attendance));
    }

    public function getDisciplineIssue(Request $request)
    {
        $studentId = $request->get('academic_info')->id;
        $filters = $request->all();

        $issues = $this->attendanceRepository
            ->getDisciplineIssue($filters, $studentId);

        return response()->json(apiResonse("Discipline Issue Fetched Successfully", $issues));
    }

    public function getClassWork(Request $request)
    {
        $request->validate(['date' => 'required|date']);
        $academicInfo = $request->get('academic_info');

        $classwork = $this->classWorkRepository
            ->getClassWorkByClassroom($academicInfo->classroom, $request->date);

        return response()->json(apiResonse("Classwork Fetched Successfully", $classwork));
    }

    public function getHomeWork(Request $request)
    {
        $request->validate(['date' => 'required|date']);
        $academicInfo = $request->get('academic_info');

        $homework = $this->homeWorkRepository
            ->getHomeWorkByClassroom($academicInfo->classroom, $request->date);

        return response()->json(apiResonse("Homework Fetched Successfully", $homework));
    }
}