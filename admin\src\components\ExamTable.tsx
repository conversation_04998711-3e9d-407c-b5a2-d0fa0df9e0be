"use client";

import React, { use<PERSON>emo, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { Exam } from "@/lib/types";
import {
  fetchExams,
  deleteExamAsync,
  openDeleteDialog,
  openFormDialog,
  closeDeleteDialog,
  closeFormDialog,
  setCurrentPage,
} from "@/app/examSlice";
import { RootState, AppDispatch } from "../app/store";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import ConfirmDialog from "@/app-components/ConfirmDialog";
import Pagination from "@/app-components/pagination";
import { DataTable } from "@/app-components/dataTable";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Badge<PERSON>he<PERSON>, ChevronUpCircleIcon, MoreH<PERSON>zon<PERSON> } from "lucide-react";
import { <PERSON>aAt, FaBan, FaEdit, FaMedal } from "react-icons/fa";
import { MdDeleteForever } from "react-icons/md";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import ExamForm from "./ExamForm";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

const PAGE_SIZE = 10;

export default function ExamTable() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const {
    exams,
    currentPage,
    totalPages,
    isDeleteDialogOpen,
    isFormDialogOpen,
    examToDelete,
    loading,
  } = useSelector((state: RootState) => state.exam);

  useEffect(() => {
    dispatch(fetchExams({ page: currentPage, limit: PAGE_SIZE }));
  }, [dispatch, currentPage]);

  const handleEdit = useCallback(
    (type: 'question' | 'form' | 'result', exam: Exam) => {
      if (type === 'question') {
        router.push(`/question/${exam.id}`);
      } else if (type === 'result') {
        router.push(`/uwhiz-result/${exam.id}`);
      } else {
        dispatch(openFormDialog(exam));
      }
    },
    [dispatch, router]
  );

  const handleDeleteClick = useCallback(
    (id: number) => {
      dispatch(openDeleteDialog(id));
    },
    [dispatch]
  );

  const handleDeleteConfirm = useCallback(() => {
    if (examToDelete !== null) {
      dispatch(deleteExamAsync(examToDelete));
    }
  }, [dispatch, examToDelete]);

  const handlePageChange = (page: number) => {
    dispatch(setCurrentPage(page));
  };

  const columns: ColumnDef<Exam>[] = useMemo(
    () => [
      {
        header: "Exam Name",
        accessorKey: "exam_name",
      },
      {
        header: "Start Date",
        accessorKey: "start_date",
        cell: ({ row }) => new Date(row.original.start_date).toLocaleString(),
      },
      {
        header: "Duration",
        accessorKey: "duration",
        cell: ({ row }) => `${row.original.duration} min`,
      },
      {
        header: "Marks",
        accessorKey: "marks",
      },
      {
        header: "Level",
        accessorKey: "level",
      },
      {
        header: "Max Participants",
        accessorKey: "total_student_intake",
      },
      {
        header: "Total Questions",
        accessorKey: "total_questions",
      },
      {
        header: "Apply Date",
        accessorKey: "start_registration_date",
        cell: ({ row }) =>
          row.original.start_registration_date
            ? new Date(row.original.start_registration_date).toLocaleString()
            : "N/A",
      },
      {
        header: "Coins Deduction",
        accessorKey: "coins_required",
      },
      {
        header: "Exam Type",
        accessorKey: "exam_type",
      },
      {
        header: "Actions",
        cell: ({ row }) => {
          const exam = row.original;
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-white hover:bg-slate-300"
                >
                  <MoreHorizontal />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem
                  onClick={() => handleEdit("form", exam)}
                  className="flex items-center gap-2"
                >
                  <FaEdit className="h-4 w-4" /> Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleEdit("result", exam)}
                  className="flex items-center gap-2"
                >
                  <BadgeCheck className="h-4 w-4" /> View Results
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleDeleteClick(exam.id)}
                  className="flex items-center gap-2"
                >
                  <MdDeleteForever className="h-4 w-4" /> Delete
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push(`/rank-price/${exam.id}`)}
                  className="flex items-center gap-2"
                >
                  <FaMedal className="h-4 w-4" /> Rank & Price
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push(`/exam-applicant/${exam.id}`)}
                  className="flex items-center gap-2"
                >
                  <FaAt className="h-4 w-4" /> Applicants
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => router.push(`/exam-prefrence/${exam.id}`)}
                  className="flex items-center gap-2"
                >
                  <ChevronUpCircleIcon className="h-4 w-4" /> Exam Weightage
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    router.push(`/uwhiz-terminated-students/${exam.id}`)
                  }
                  className="flex items-center gap-2"
                >
                  <FaBan className="h-4 w-4" /> Terminated Students
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
    ],
    [handleEdit, handleDeleteClick, router]
  );

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-3">
        <h1 className="text-2xl font-bold">Exams</h1>
        <Button onClick={() => dispatch(openFormDialog(null))}>Add Exam</Button>
      </div>

      <DataTable
        columns={columns}
        data={exams}
        isLoading={loading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${exams.length} entries`}
      />

      <ConfirmDialog
        open={isDeleteDialogOpen}
        setOpen={(val) => !val && dispatch(closeDeleteDialog())}
        title="Are you sure?"
        description="This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        isLoading={loading}
      />

      <Dialog
        open={isFormDialogOpen}
        onOpenChange={(open) => !open && dispatch(closeFormDialog())}
      >
        <DialogContent
          onInteractOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <VisuallyHidden>
              <DialogTitle>Add/Edit Exam</DialogTitle>
            </VisuallyHidden>
            <DialogDescription className="sr-only">
              Form to add or edit exam
            </DialogDescription>
            <DialogClose asChild>
              <button className="absolute right-4 top-4 rounded-sm opacity-70 hover:opacity-100 focus:outline-none">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </DialogClose>
          </DialogHeader>
          <ExamForm />
        </DialogContent>
      </Dialog>
    </div>
  );
}