<?php

namespace HomeWork\Repositories;

use Illuminate\Support\Facades\Auth;
use HomeWork\Interfaces\HomeWorkInterface;
use HomeWork\Models\HomeWork;
use Illuminate\Support\Facades\DB;

class HomeWorkRepository implements HomeWorkInterface
{
    protected $homeWork;
    public function __construct(HomeWork $homeWork)
    {
        $this->homeWork = $homeWork;
    }

    public function getAll($request)
    {
        $homeWorks = $this->homeWork::query()->with('classroom', 'subject')
        ->where('class_uuid', Auth::id());

        searchColumn($request->input('columns'), $homeWorks);
        orderColumn($request, $homeWorks, 'homeworks.id');

        return $homeWorks;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('', function ($data) {
                return $data->job_title;
            })
            ->addColumn('classroom_id', function ($data) {
                return optional($data->classroom)->class_name ?? 'N/A';
            })
            ->addColumn('subject_id', function ($data) {
                return optional($data->subject)->subject_name ?? 'N/A';
            })
            ->addColumn('homework_date', function ($data) {
                return $data->homework_date;
            })
            ->addColumn('title', function ($data) {
                return $data->title;
            })
            ->addColumn('description', function ($data) {
                return $data->description;
            })
            ->addColumn('action', function ($data) {
                $button = '';
                if (Auth::user()->can('update homeWork')) {
                    $button .= '<button data-toggle="modal"
                                    data-target="#newHomeWorkEntry"
                                    data-editHomeWorkid="' . $data->id . '"
                                    class="btn editHomeWorkEntry">
                                    <i class="fas fa-edit"></i>
                                </button>';
                }
                if (Auth::user()->can('delete homeWork')) {
                    $button .= '<button type="button"
                    class="deleteHomeWorkEntry btn" title="Delete"
                    data-homeWorkid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
                }
                return $button;
            })->rawColumns(['action', 'description'])
            ->make(true);
    }

    public function storeHomeWork($request)
    {
        HomeWork::create([
            'class_uuid'     => Auth::user()->id,
            'classroom_id'   => $request['classroom_id'],
            'subject_id'     => $request['subject_id'],
            'homework_date' => $request['homework_date'],
            'title'          => $request['title'],
            'description'    => $request['description'],
        ]);
    }

    public function updateHomeWork($data, $id)
    {
        $updatedata = $this->getHomeWorkById($id);
        $updatedata->update($data->all());
    }

    public function getHomeWorkById($id)
    {
        return $this->homeWork::Find($id);
    }

    public function getAllhomeWorks()
    {
        return $this->homeWork::select('id', 'class_uuid', 'classroom_id', 'subject_id', 'homework_date', 'title', 'description')->get();
    }

    public function getHomeWorkByClassroom($classId, $date)
    {
        return $this->homeWork
            ->leftJoin(DB::raw('subjects'), DB::raw('CAST(homeworks.subject_id AS BIGINT)'), '=', 'subjects.id')
            ->where('homeworks.classroom_id', $classId)->whereDate('homework_date',$date)
            ->orderBy('homeworks.id', 'desc')
            ->select('homeworks.*', 'subjects.subject_name')
            ->get();
    }
}
