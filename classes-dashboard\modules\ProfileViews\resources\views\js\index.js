// Profile Views Table Columns
var columns = [
    {
        data: "class_name",
        name: "class_name",
        orderable: false
    },
    {
        data: "student_name",
        name: "Student.firstName",
        orderable: false
    },
    {
        data: "student_email",
        name: "student_email",
        orderable: false
    },
    {
        data: "viewed_at",
        name: "viewed_at",
        orderable: false
    },
];

// Initialize Profile Views Table
var table = commonDatatable(
    "#profileviews_table",
    profileviewsRoute.index,
    columns
);