"use client";

import { useEffect, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import axiosInstance from "@/lib/axios";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";
import { ExamApplication } from "@/lib/types";

export default function ExamRankingPage() {
  const [data, setData] = useState<ExamApplication[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [loading, setLoading] = useState(false);
  const params = useParams();
  const examId = params.examId;

  useEffect(() => {
    if (!examId) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        const response = await axiosInstance.get(`/examApplication/${examId}?page=${page}&limit=${limit}`, {
          headers: {
            "Server-Select": "uwhizServer",
          },
        });
        setData(response.data.data);
        setTotal(response.data.total);
      } catch (error: any) {
        console.error("Failed to fetch data:", error.response?.data?.message || error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [examId, page, limit]);

  const columns: ColumnDef<ExamApplication, unknown>[] = [
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "contact",
      header: "Contact",
      cell: (info) => info.getValue(),
    },
  ];

  const totalPages = Math.ceil(total / limit);

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Exam Applicants</h1>

      <DataTable
        columns={columns}
        data={data}
        isLoading={loading}
      />

      <Pagination
        page={page}
        totalPages={totalPages}
        setPage={setPage}
        entriesText={`${total} entries`}
      />
    </div>
  );
}