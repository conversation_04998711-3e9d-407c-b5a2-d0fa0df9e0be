<?php

namespace Testimonial\Repositories;

use Testimonial\Models\Testimonial;
use Testimonial\Interfaces\TestimonialInterface;
use Illuminate\Support\Facades\Auth;

class TestimonialRepository implements TestimonialInterface
{
    protected $testimonial;
    function __construct(Testimonial $testimonial)
    {
        $this->testimonial = $testimonial;
    }

    public function getAll($request)
    {
        $testimonial = $this->testimonial::where('classId', Auth::id());
    
        searchColumn($request->input('columns'), $testimonial);
        orderColumn($request, $testimonial, 'Testimonial.id');

        return $testimonial;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
        ->addColumn('message', function ($data) {
            return $data->message;
        })
        ->addColumn('createdAt', function ($data) {
            return $data->createdAt;
        })
        ->addColumn('rating', function ($data) {
            $stars = '';
            $rating = (int) $data->rating;

            for ($i = 1; $i <= 5; $i++) {
                if ($i <= $rating) {
                    $stars .= '<i class="fa fa-star text-warning"></i>'; // filled star
                } else {
                    $stars .= '<i class="fa fa-star text-secondary"></i>'; // empty star
                }
            }

            return $stars;
        })
        ->addColumn('status', function ($data) {
            return status_color($data->status);
        })
        ->addColumn('action', function ($data) {
            $button = "";
            if ($data->status != "APPROVED") {
                $button .= '<button type="button" class="deleteTestimonialEntry btn" title="Delete" data-deletetestimonialid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
            }
            return $button;
        })->rawColumns(['action', 'status', 'rating'])
        ->make(true);
    }

    public function createTestimonial($data)
    {
        $this->testimonial::create([
            'id' => generateUUID(),
            'message' => $data->testimonial,
            'rating'  => $data->rating,
            'classId' => Auth::id(),
            'createdAt' => now(),
            'updatedAt' => now(),
        ]);
    }

    public function getTestimonialById($id)
    {
        return $this->testimonial::Find($id);
    }
}
