var columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "classwork_date",
        name: "classwork_date",
    },
    {
        data: "classroom_id",
        name: "classroom_id",
    },
    {
        data: "subject_id",
        name: "subject_id",
    },
    {
        data: "title",
        name: "title",
    },
    {
        data: "description",
        name: "description",
    },
];

var table = commonDatatable(
    "#classWork_table",
    classWorksRoute.index,
    columns
);

$(document).on("click", "#addClassWorkEntry", function () {
    var params = $.extend({}, doAjax_params_default);
    params["url"] = classWorksRoute.create;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Add ClassWork");
        $("#createContent").html(result);
    },
        commonAjax(params);
});

$(document).on("click", ".editClassWorkEntry", function () {
    let editdid = $(this).attr("data-editClassWorkid");
    let url = classWorksRoute.edit;
    url = url.replace(":editdid", editdid);

    let params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        $("#modeltitle").html("Edit ClassWork");
        $("#createContent").html(result);
    };
    commonAjax(params);
});


$(document).on("click", ".deleteClassWorkEntry", function () {
    var classWorkid = $(this).attr("data-classWorkid");
    var url = classWorksRoute.delete;
    url = url.replace(":classWorkid", classWorkid);

    var params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `DELETE`;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        toastr.success(result.success);
        table.draw();
    };
    var calert = function calert() {
        commonAjax(params);
    };
    commonAlert(calert);
});
