<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'homeWork.store', 'enctype' => 'multipart/form-data' ,'id'=>'createhomeWork_form']) !!}
                    @include('HomeWork::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('HomeWork\Http\Requests\CreateHomeWorkRequest', '#createhomeWork_form') !!}
<script>
     var createHomeWorkRoute = {
        store: "{{ route('homeWork.store') }}",
    };
    initializeCKEditor($(".editor")[0]);
</script>

<script src="{{ asset(mix('js/page-level-js/HomeWork/create.js')) }}"></script>