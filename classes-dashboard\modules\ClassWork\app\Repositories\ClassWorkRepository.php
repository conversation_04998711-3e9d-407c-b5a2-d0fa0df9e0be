<?php

namespace ClassWork\Repositories;

use Illuminate\Support\Facades\Auth;
use ClassWork\Interfaces\ClassWorkInterface;
use ClassWork\Models\ClassWork;
use Illuminate\Support\Facades\DB;

class ClassWorkRepository implements ClassWorkInterface
{
    protected $classWork;
    public function __construct(ClassWork $classWork)
    {
        $this->classWork = $classWork;
    }

    public function getAll($request)
    {
        $classWorks = $this->classWork::query()->with('classroom', 'subject')
        ->where('class_uuid', Auth::id());

        searchColumn($request->input('columns'), $classWorks);
        orderColumn($request, $classWorks, 'classworks.id');

        return $classWorks;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('', function ($data) {
                return $data->job_title;
            })
            ->addColumn('classroom_id', function ($data) {
                return optional($data->classroom)->class_name ?? 'N/A';
            })
            ->addColumn('subject_id', function ($data) {
                return optional($data->subject)->subject_name ?? 'N/A';
            })
            ->addColumn('classwork_date', function ($data) {
                return $data->classwork_date;
            })
            ->addColumn('title', function ($data) {
                return $data->title;
            })
            ->addColumn('description', function ($data) {
                return $data->description;
            })
            ->addColumn('action', function ($data) {
                $button = '';
                if (Auth::user()->can('update classWork')) {
                    $button .= '<button data-toggle="modal"
                                    data-target="#newClassWorkEntry"
                                    data-editClassWorkid="' . $data->id . '"
                                    class="btn editClassWorkEntry">
                                    <i class="fas fa-edit"></i>
                                </button>';
                }
                if (Auth::user()->can('delete classWork')) {
                    $button .= '<button type="button"
                    class="deleteClassWorkEntry btn" title="Delete"
                    data-classWorkid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
                }
                return $button;
            })->rawColumns(['action', 'description'])
            ->make(true);
    }

    public function storeClassWork($request)
    {
        ClassWork::create([
            'class_uuid'     => Auth::user()->id,
            'classroom_id'   => $request['classroom_id'],
            'subject_id'     => $request['subject_id'],
            'classwork_date' => $request['classwork_date'],
            'title'          => $request['title'],
            'description'    => $request['description'],
        ]);
    }

    public function updateClassWork($data, $id)
    {
        $updatedata = $this->getClassWorkById($id);
        $updatedata->update($data->all());
    }

    public function getClassWorkById($id)
    {
        return $this->classWork::Find($id);
    }

    public function getAllClassWorks()
    {
        return $this->classWork::select('id', 'class_uuid', 'classroom_id', 'subject_id', 'classwork_date', 'title', 'description')->get();
    }

    public function getClassWorkByClassroom($classId, $date)
    {
        return $this->classWork
            ->leftJoin(DB::raw('subjects'), DB::raw('CAST(classworks.subject_id AS BIGINT)'), '=', 'subjects.id')
            ->where('classworks.classroom_id', $classId)->whereDate('classwork_date',$date)
            ->orderBy('classworks.id', 'desc')
            ->select('classworks.*', 'subjects.subject_name')
            ->get();
    }
}
