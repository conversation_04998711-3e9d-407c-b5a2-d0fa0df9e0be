<?php

namespace StudentLeave\Repositories;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use StudentLeave\Models\StudentLeave;
use StudentLeave\Interfaces\StudentLeaveInterface;

class StudentLeaveRepository implements StudentLeaveInterface
{
    protected $studentLeaves;

    function __construct(StudentLeave $studentLeaves)
    {
        $this->studentLeaves = $studentLeaves;
    }

    public function getAll($data)
    {
        $list = $this->studentLeaves
            ->leftJoin('student_details_view', 'student_details_view.student_academic_id', '=', 'student_leaves.student_id')
            ->select(
                'student_leaves.*',
                'student_details_view.first_name',
                'student_details_view.last_name',
                'student_details_view.classroom_id as student_class_id',
                'student_details_view.department_id as student_department_id',
                'student_details_view.year_name',
                DB::raw("CONCAT(student_details_view.first_name, ' ', student_details_view.last_name) AS student_full_name"),
                DB::raw("CONCAT(student_details_view.class_name, ' (', student_details_view.department_name, ')') AS student_class_info")
            )
            ->when($data['status'], function ($query) use ($data) {
                return $query->where('leave_status', $data['status']);
            })
            ->when($data['start_date'], function ($query) use ($data) {
                return $query->whereDate('leave_date', '>=', $data['start_date']);
            })
            ->when($data['end_date'], function ($query) use ($data) {
                return $query->whereDate('leave_date', '<=', $data['end_date']);
            })
            ->where('student_details_view.year_id', getActiveYearId());

        $concatenatedColumns = [];
        searchColumn($data->input('columns'), $list, $concatenatedColumns);
        orderColumn($data, $list, 'student_leaves.id');

        return $list;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
            ->addColumn('student_full_name', function ($data) {
                return $data->student_full_name;
            })
            ->addColumn('class_name', function ($data) {
                return $data->student_class_info;
            })
            ->addColumn('leave_date', function ($data) {
                return $data->leave_date;
            })
            ->addColumn('reason', function ($data) {
                return $data->reason;
            })
            ->addColumn('leave_status', function ($data) {
                $status = $data->leave_status;
                if ($status == config('constants.STATUS.APPROVED')) {
                    return '<span class="badge bg-success">' . $status . '</span>';
                } elseif ($status == config('constants.STATUS.REJECT')) {
                    return '<span class="badge bg-danger">' . $status . '</span>';
                } else {
                    return '<span class="badge bg-warning">' . $status . '</span>';
                }
            })
            ->addColumn('action', function ($data) {
                if ($data->leave_status == config('constants.STATUS.APPROVED')) {
                    $button = '<button disabled title="Approve" class="btn bg-success"><i class="nav-icon fa fa-thumbs-up"></i></button>
                    <button disabled title="Reject" class="btn bg-danger"><i class="nav-icon fa fa-thumbs-down"></i></button>';
                    return $button;
                } else {
                    $button = '
                    <button data-leaveid="' . $data->id . '" value="' . config('constants.STATUS.APPROVED') . '" title="Approve" class="btn bg-success leavestatus"><i class="nav-icon fa fa-thumbs-up"></i></button>
                    <button data-leaveid="' . $data->id . '" value="' . config('constants.STATUS.REJECT') . '" title="Reject" class="btn bg-danger leavestatus"><i class="nav-icon fa fa-thumbs-down"></i></button>';
                    return $button;
                }
            })
            ->rawColumns(['leave_status', 'action'])
            ->make(true);
    }

    public function leaveapprove($request, $id)
    {
        $leave = $this->studentLeaves::findOrFail($id);
        $leave->leave_status = $request->subbtn;
        $leave->save();
    }

    public function multipleapprove($request)
    {
        $requestids = $request->id;
        foreach ($requestids as $id) {
            $this->leaveapprove($request, $id);
        }
    }

    public function exportQuery($request, $type) {}
}