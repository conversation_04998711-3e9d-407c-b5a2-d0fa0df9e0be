import { z } from 'zod';

export const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name must be less than 100 characters'),
});

export const updateCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required').max(100, 'Category name must be less than 100 characters'),
});

export const createDetailSchema = z.object({
  name: z.string().min(1, 'Detail name is required').max(100, 'Detail name must be less than 100 characters'),
  categoryId: z.string().uuid('Invalid category ID'),
});

export const updateDetailSchema = z.object({
  name: z.string().min(1, 'Detail name is required').max(100, 'Detail name must be less than 100 characters'),
});

export const createSubDetailSchema = z.object({
  name: z.string().min(1, 'Sub-detail name is required').max(100, 'Sub-detail name must be less than 100 characters'),
  detailId: z.string().uuid('Invalid detail ID'),
});

export const updateSubDetailSchema = z.object({
  name: z.string().min(1, 'Sub-detail name is required').max(100, 'Sub-detail name must be less than 100 characters'),
});

export const createValueSchema = z.object({
  name: z.string().min(1, 'Value name is required').max(100, 'Value name must be less than 100 characters'),
  subDetailId: z.string().uuid('Invalid sub-detail ID'),
  isActive: z.boolean().optional().default(true),
});

export const updateValueSchema = z.object({
  name: z.string().min(1, 'Value name is required').max(100, 'Value name must be less than 100 characters'),
  isActive: z.boolean().optional(),
});



export type CreateCategoryRequest = z.infer<typeof createCategorySchema>;
export type UpdateCategoryRequest = z.infer<typeof updateCategorySchema>;
export type CreateDetailRequest = z.infer<typeof createDetailSchema>;
export type UpdateDetailRequest = z.infer<typeof updateDetailSchema>;
export type CreateSubDetailRequest = z.infer<typeof createSubDetailSchema>;
export type UpdateSubDetailRequest = z.infer<typeof updateSubDetailSchema>;
export type CreateValueRequest = z.infer<typeof createValueSchema>;
export type UpdateValueRequest = z.infer<typeof updateValueSchema>;

