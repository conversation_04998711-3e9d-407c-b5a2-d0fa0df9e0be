import { axiosInstance } from '../lib/axios';

interface RegisterData {
  firstName: string;
  lastName: string;
  contactNo: string;
  referralCode?: string;
}

interface LoginData {
  contactNo: string;
  email?: string;
}

interface VerifyOtpData {
  contactNo: string;
  otp: string;
  email?: string;
  firstName?: string;
  lastName?: string;
}

interface ResendOtpData {
  contactNo: string;
  firstName?: string;
}

interface ContinueWithEmailData {
  email: string;
}

export async function continueWithEmail(data: ContinueWithEmailData) {
  const response = await axiosInstance.post('/auth-client/continue-with-email', data);
  return response.data;
}

export async function registerUser(data: RegisterData) {
  const response = await axiosInstance.post('/auth-client/register', data);
  return response.data;
}

export async function loginUser(data: LoginData) {
  const response = await axiosInstance.post('/auth-client/login', data);
  return response.data;
}

export async function verifyOtp(data: VerifyOtpData) {
  const response = await axiosInstance.post('/auth-client/verify-otp', data);
  return response.data;
}

export async function resendOtp(data: ResendOtpData) {
  const response = await axiosInstance.post('/auth-client/resend-otp', data);
  return response.data;
}

export function logoutUser(): void {
  localStorage.removeItem('user');
}

export const generateJWT = async (contact: string | undefined, password : string | undefined) => {
  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });
  return response.data;
};



export const verifyEmail = async (token: string) => {
  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });
  return response.data;
};

export const resendVerificationEmail = async (email: string) => {
  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });
  return response.data;
};