import prisma from '@/config/prismaClient';
import { Student } from '@prisma/client';


export const findStudentByGoogleId = (googleId: string) => {
  return prisma.student.findUnique({
    where: { googleId }
  });
};

export const findStudentByEmail = (email: string) => {
  return prisma.student.findUnique({
    where: { email }
  });
};

export const createStudentWithGoogle = (
  firstName: string,
  lastName: string,
  email: string,
  googleId: string,
  profilePhoto?: string
): Promise<Student> => {
  return prisma.student.create({
    data: {
      firstName: firstName || 'Google',
      lastName: lastName || 'User',
      email,
      googleId,
      profilePhoto,
      isVerified: true, 
      contact: '', 
      // password: '', 
    },
  });
};


export const updateStudentWithGoogle = (
  id: string,
  googleId: string,
  profilePhoto?: string
): Promise<Student> => {
  return prisma.student.update({
    where: { id },
    data: {
      googleId,
      profilePhoto,
      isVerified: true, 
    },
  });
};
