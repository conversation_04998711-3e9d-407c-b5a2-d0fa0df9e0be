import { axiosInstance } from "@/lib/axios";

export const getMockExamLeaderboard = async (
  timeframe: string,
  page: number = 1,
  limit: number = 10
): Promise<any> => {
  try {
    const response = await axiosInstance.get(
      `/mock-exam-leaderboard/leaderboard/${timeframe}?page=${page}&limit=${limit}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get mock exam leaderboard data: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};