<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Model;
use Routes\Models\Route;
use StudentsModule\Models\Transport\WaypointFees;

class StudentDetailsView extends Model
{
    public $table = 'student_details_view';
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;
    public $timestamps = false;

    public function getAcademicInfo()
    {
        return $this->belongsTo(StudentAcademicInfo::class, 'id', 'student_id')->withDefault();
    }
}

