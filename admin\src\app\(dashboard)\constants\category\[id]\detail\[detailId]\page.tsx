'use client';

import React, { useState, useEffect, useC<PERSON>back, useMemo } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Plus, Search, Trash2, Edit, ArrowLeft, FolderOpen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import Pagination from '@/app-components/pagination';
import {
  getDetailById,
  getSubDetailsByDetail,
  createSubDetail,
  updateSubDetail,
  deleteSubDetail
} from '@/services/constantsApi';
import { ConstantDetail, ConstantSubDetail } from '@/lib/types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

export default function DetailSubDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const categoryId = params.id as string;
  const detailId = params.detailId as string;

  const [detail, setDetail] = useState<ConstantDetail | null>(null);
  const [subDetails, setSubDetails] = useState<ConstantSubDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState('');

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingSubDetail, setEditingSubDetail] = useState<ConstantSubDetail | null>(null);
  const [newSubDetailName, setNewSubDetailName] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingSubDetail, setDeletingSubDetail] = useState<ConstantSubDetail | null>(null);


  const fetchDetail = useCallback(async () => {
    try {
      const data = await getDetailById(detailId);
      setDetail(data);
    } catch {
      toast.error('Detail not found');
    }
  }, [detailId]);


  const fetchSubDetails = useCallback(async (page: number = currentPage, search?: string) => {
    try {
      setLoading(true);
      const result = await getSubDetailsByDetail(detailId, page, pageSize, search);
      setSubDetails(result.data);
      setTotalPages(result.pagination.totalPages);
      setTotalRecords(result.pagination.total);
    } catch {
      toast.error('Failed to fetch sub-details');
    } finally {
      setLoading(false);
    }
  }, [detailId, currentPage, pageSize]);

  const createSubDetailHandler = async () => {
    if (!newSubDetailName.trim()) {
      toast.error('Sub-detail name is required');
      return;
    }

    try {
      await createSubDetail({
        name: newSubDetailName,
        detailId: detailId
      });
      toast.success('Sub-detail created successfully');
      setNewSubDetailName('');
      setIsAddDialogOpen(false);
      fetchSubDetails();
    } catch {
      toast.error('Failed to create sub-detail');
    }
  };

  const updateSubDetailHandler = async () => {
    if (!editingSubDetail || !newSubDetailName.trim()) {
      toast.error('Sub-detail name is required');
      return;
    }

    try {
      await updateSubDetail(editingSubDetail.id, { name: newSubDetailName });
      toast.success('Sub-detail updated successfully');
      setNewSubDetailName('');
      setIsEditDialogOpen(false);
      setEditingSubDetail(null);
      fetchSubDetails();
    } catch {
      toast.error('Failed to update sub-detail');
    }
  };


  const handleDeleteClick = useCallback((subDetail: ConstantSubDetail) => {
    setDeletingSubDetail(subDetail);
    setIsDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(async () => {
    if (!deletingSubDetail) return;

    try {
      await deleteSubDetail(deletingSubDetail.id);
      toast.success('Sub-detail deleted successfully');
      setCurrentPage(1);
      fetchSubDetails(1);
    } catch {
      toast.error('Cannot delete sub-detail as it contains values or is being used');
    } finally {
      setIsDeleteDialogOpen(false);
      setDeletingSubDetail(null);
    }
  }, [deletingSubDetail, fetchSubDetails]);

  const handleEdit = useCallback((subDetail: ConstantSubDetail) => {
    setEditingSubDetail(subDetail);
    setNewSubDetailName(subDetail.name);
    setIsEditDialogOpen(true);
  }, []);



  const applySearch = useCallback(() => {
    setAppliedSearchTerm(searchTerm);
    setCurrentPage(1);
    fetchSubDetails(1, searchTerm.trim() || undefined);
  }, [searchTerm, fetchSubDetails]);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setAppliedSearchTerm('');
    setCurrentPage(1);
    fetchSubDetails(1, undefined);
  }, [fetchSubDetails]);

  const handleSearchKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applySearch();
    }
  }, [applySearch]);



  const columns = useMemo<ColumnDef<ConstantSubDetail>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('name')}</div>
      ),
    },
    {
      id: 'values',
      header: 'Values',
      cell: ({ row }) => (
        <Badge variant="secondary">{row.original.values.length}</Badge>
      ),
    },
    {
      id: 'actions',
      header: () => (
        <div className="text-right">Actions</div>
      ),
      cell: ({ row }) => (
        <div className="flex items-center justify-end space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/constants/category/${categoryId}/detail/${detailId}/sub-detail/${row.original.id}`)}
            title="Manage Values"
            className="h-8 w-8 p-0"
          >
            <FolderOpen className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            title="Edit Sub-Detail"
            className="h-8 w-8 p-0"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(row.original)}
            title="Delete Sub-Detail"
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
      enableSorting: false,
      enableHiding: false,
    },
  ], [handleEdit, handleDeleteClick, router, categoryId, detailId]);


  useEffect(() => {
    fetchDetail();
    fetchSubDetails(1);
  }, [fetchDetail]);

  useEffect(() => {
    if (appliedSearchTerm) {
      fetchSubDetails(currentPage, appliedSearchTerm);
    } else {
      fetchSubDetails(currentPage);
    }
  }, [currentPage, appliedSearchTerm, fetchSubDetails]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading sub-details...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">
              {detail?.category.name} → {detail?.name} - Sub-Details
            </h1>
            <p className="text-gray-600 mt-1">Manage sub-details and their values</p>
          </div>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Sub-Detail
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Sub-Detail</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="subDetailName">Sub-Detail Name</Label>
                <Input
                  id="subDetailName"
                  value={newSubDetailName}
                  onChange={(e) => setNewSubDetailName(e.target.value)}
                  placeholder="Enter sub-detail name"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={createSubDetailHandler}>Create</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>


      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search sub-details..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyPress}
              className="pl-10 w-64"
            />
          </div>
          <Button
            onClick={applySearch}
            size="sm"
            disabled={!searchTerm.trim()}
            className="bg-black text-white hover:bg-gray-800"
          >
            Search
          </Button>
          {appliedSearchTerm && (
            <Button
              onClick={clearSearch}
              variant="ghost"
              size="sm"
            >
              Clear
            </Button>
          )}
        </div>

      </div>


      <Card>
        <CardHeader>
          <CardTitle>Sub-Details ({totalRecords})</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <DataTable
            columns={columns}
            data={subDetails}
            isLoading={loading}
          />
        </CardContent>
      </Card>

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={(page) => {
          setCurrentPage(page);
          fetchSubDetails(page);
        }}
        entriesText={`${totalRecords} entries`}
      />


      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sub-Detail</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editSubDetailName">Sub-Detail Name</Label>
              <Input
                id="editSubDetailName"
                value={newSubDetailName}
                onChange={(e) => setNewSubDetailName(e.target.value)}
                placeholder="Enter sub-detail name"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={updateSubDetailHandler}>Update</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>


      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete the sub-detail <strong>&quot;{deletingSubDetail?.name}&quot;</strong>?</p>
            <p className="text-sm text-gray-600">This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={confirmDelete}
              >
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
