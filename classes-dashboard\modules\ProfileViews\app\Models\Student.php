<?php

namespace ProfileViews\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $table = 'Student'; // Exact table name from Prisma schema
    protected $connection = 'main';
    
    protected $keyType = 'string';
    public $incrementing = false;
    
    protected $fillable = [
        'id',
        'firstName',
        'lastName',
        'email',
    ];
}