@extends('layouts.app')
@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-12 main-title-flex">
                    <h1>Edit Blogs</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-default">
                    <div class="card-body">
                        {!! Form::model($data, ['id' => 'editblogs_form', 'route' => ['blogs.update', $data->id]]) !!}
                        @include('Blogs::fields')
                        {!! Form::close() !!}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    {!! JsValidator::formRequest('Blogs\Http\Requests\CreateBlogsRequest', '#editblogs_form') !!}
    <script>
        var createblogsRoute = {
            store: "{{ route('blogs.store') }}",
        };
    </script>
    <script src="{{ asset('assets/js/ckeditor.js') }}"></script>
    <script>
        initializeCKEditor($(".editor")[0]);
    </script>
    <script>
        $(document).ready(function() {
            $('#featureImageInput').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Only JPG, JPEG, and PNG files are allowed');
                        $(this).val('');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#featureImagePreview').attr('src', e.target.result).show();
                    };
                    reader.readAsDataURL(file);
                }
            });

            $('#editblogs_form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                if ($(this).valid()) {

                    $.ajax({
                        url: '{{ env('UEST_FRONTEND_URL') }}/api/v1/blogs/' + "{{ $data->id }}",
                        method: 'put',
                        data: formData,
                        contentType: false,
                        processData: false,
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function(response) {
                            toastr.success('Blog Updated successfully!');
                            $('#imagePreview').hide();

                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        },
                        error: function(xhr, status, error) {
                            const message = xhr.responseJSON?.message ||
                                'Failed to submit the blog.';
                            toastr.error(message);
                            console.error('Error:', xhr, error);
                        }
                    });
                }
            });
        });
    </script>
@endsection
