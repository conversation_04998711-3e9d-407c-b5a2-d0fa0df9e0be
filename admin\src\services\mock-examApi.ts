import axiosInstance from "../lib/axios";
import { MockQuestionBankInput } from "../lib/types";

//Get all Question Bank Detail
export const getMockQuestionBank = async (
  page: number = 1,
  limit: number = 10,
): Promise<any> => {
  try {
    const response = await axiosInstance.get(`mock-exam-questionBank?page=${page}&limit=${limit}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch mock question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const createMockQuestionBank = async (
  data: MockQuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.post("mock-exam-questionBank", data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const updateMockQuestionBank = async (
  id: string,
  data: MockQuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.put(`mock-exam-questionBank/${id}`, data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const deleteMockQuestionBank = async (id: string): Promise<any> => {
  try {
    const response = await axiosInstance.delete(`mock-exam-questionBank/${id}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const deleteManyMockQuestions = async (ids: string[]): Promise<any> => {
  try {
    const response = await axiosInstance.delete('/mock-exam-questionBank/bulk/delete', {
      data: { ids },
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete questions: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const importExcelQuestions = async (file: File): Promise<any> => {
  try {
    const formData = new FormData();
    formData.append("excelFile", file);
    const response = await axiosInstance.post("/mock-exam-questionBank/import", formData, {
      headers: {
        "Server-Select": "uwhizServer",
        "Content-Type": "multipart/form-data",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to import questions: ${
        error.response?.data?.error || error.message
      }`,
    };
  }
};