<?php

namespace HomeWork\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use HomeWork\Http\Requests\CreateHomeWorkRequest;
use HomeWork\Repositories\HomeWorkRepository;
use Classroom\Repositories\ClassroomRepository;
use Subject\Repositories\SubjectRepository;

class HomeWorkController extends Controller
{
    protected $homeWorkRepository;
    protected $classroomRepository;
    protected $subjectRepository;

    public function __construct(HomeWorkRepository $homeWorkRepository, ClassroomRepository $classroomRepository, SubjectRepository $subjectRepository)
    {
        $this->middleware('permission:read homeWork', ['only' => ['index']]);
        $this->middleware('permission:create homeWork', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete homeWork', ['only' => ['delete','destroy']]);
        $this->middleware('permission:update homeWork', ['only' => ['edit', 'update']]);
        $this->homeWorkRepository = $homeWorkRepository;
        $this->classroomRepository = $classroomRepository;
        $this->subjectRepository = $subjectRepository;
    }
   
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->homeWorkRepository->getAll($request);
            return $this->homeWorkRepository->getDatatable($list);
            dd($list);
        }
      return view('HomeWork::index');
    }

    public function create(Request $request)
    {
        $classroom = $this->classroomRepository->getAll($request)->get();
        $subject = $this->subjectRepository->getAll($request)->get();
        return view('HomeWork::create', compact('classroom', 'subject'));
    }

    public function store(CreateHomeWorkRequest $request)
    {
        $this->homeWorkRepository->storeHomeWork($request);
        return response()->json(['success' => 'Homework Added Successfully!!']);
    }

    public function edit($id, Request $request)
    {
        $data = $this->homeWorkRepository->getHomeWorkById($id);
        $classroom = $this->classroomRepository->getAll($request)->get();
        $subject = $this->subjectRepository->getAll($request)->get();
        return view('HomeWork::edit', compact('data', 'classroom', 'subject'));
    }

    public function update(CreateHomeWorkRequest $request, $id)
    {
        $this->homeWorkRepository->updateHomeWork($request, $id);
        return response()->json(['success' => 'Homework Updated Successfully!!']);
    }

    public function destroy($id)
    {
        $job = $this->homeWorkRepository->getHomeWorkById($id);
        $job->delete($id);
        return response()->json(['success' => 'Homework deleted successfully!!']);
    }

    public function getAllHomeWorks()
    {
        $data = $this->homeWorkRepository->getAllHomeWorks();
        return response()->json(['success' => 'Homework fetched successfully!!', 'data' => $data]);
    }

    public function getHomeWorkById($id)
    {
        $data = $this->homeWorkRepository->getHomeWorkById($id);
        return response()->json(['success' => 'Homework fetched successfully!!', 'data' => $data]);
    }
}
