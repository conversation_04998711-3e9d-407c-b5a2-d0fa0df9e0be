<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['route' => ['classWork.update', $data->id],'id'=>'editclassWorks_form']) !!}
                        @include('ClassWork::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('ClassWork\Http\Requests\CreateClassWorkRequest', '#editclassWorks_form') !!}
<script>
     var editClassWorkRoute = {
        update: "{{ route('classWork.update', $data->id) }}",
    };
    initializeCKEditor($(".editor")[0]);
</script>

<script src="{{ asset(mix('js/page-level-js/ClassWork/edit.js')) }}"></script>