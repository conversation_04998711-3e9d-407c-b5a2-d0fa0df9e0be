'use client';
import { useState, useEffect, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    flexRender,
    getCoreRowModel,
    useReactTable,
    ColumnDef,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    <PERSON>ertD<PERSON>ogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Pencil, Trash2, ChevronLeft, ChevronRight, Download, Upload } from 'lucide-react';
import { toast } from 'sonner';
import {
    createMockQuestionBank,
    updateMockQuestionBank,
    deleteMockQuestionBank,
    getMockQuestionBank,
    deleteManyMockQuestions,
    importExcelQuestions,
} from '@/services/mock-examApi';
import { CalendarIcon } from 'lucide-react';
import { MockQuestionBank } from '@/lib/types';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';
const questionBankSchema = z.object({
    question: z.string().min(1, { message: 'Question is required' }).max(500, { message: 'Question cannot exceed 500 characters' }),
    optionOne: z.string().min(1, { message: 'Option 1 is required' }).max(100, { message: 'Option 1 cannot exceed 100 characters' }),
    optionTwo: z.string().min(1, { message: 'Option 2 is required' }).max(100, { message: 'Option 2 cannot exceed 100 characters' }),
    optionThree: z.string().min(1, { message: 'Option 3 is required' }).max(100, { message: 'Option 3 cannot exceed 100 characters' }),
    optionFour: z.string().min(1, { message: 'Option 4 is required' }).max(100, { message: 'Option 4 cannot exceed 100 characters' }),
    correctAnswer: z.enum(['optionOne', 'optionTwo', 'optionThree', 'optionFour'], {
        errorMap: () => ({ message: 'Please select a correct answer' }),
    }),
    quetionDate: z.date({
        required_error: "Question date is required",
        invalid_type_error: "Please select a valid date",
    }),
});

type QuestionBankFormInput = z.infer<typeof questionBankSchema>;

const defaultFormValues: QuestionBankFormInput = {
    question: '',
    optionOne: '',
    optionTwo: '',
    optionThree: '',
    optionFour: '',
    correctAnswer: 'optionOne',
    quetionDate: new Date(),
};

export default function QuestionBankPage() {
    const [questions, setQuestions] = useState<MockQuestionBank[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
    const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false); // New state for bulk delete dialog
    const [editingQuestion, setEditingQuestion] = useState<MockQuestionBank | null>(null);
    const [apiError, setApiError] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalQuestions, setTotalQuestions] = useState(0);
    const [limit, setLimit] = useState(10);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const {
        register,
        handleSubmit,
        reset,
        control,
        formState: { errors },
    } = useForm<QuestionBankFormInput>({
        resolver: zodResolver(questionBankSchema),
        defaultValues: defaultFormValues,
    });

    const fetchQuestions = async (page: number = 1, limit: number = 10) => {
        setIsLoading(true);
        const response = await getMockQuestionBank(page, limit);
        if (response.success && response.data) {
            setQuestions(response.data.data);
            setTotalPages(response.data.pagination.totalPages);
            setTotalQuestions(response.data.pagination.totalQuestions);
            setCurrentPage(page);
        } else {
            setQuestions([]);
            setTotalPages(1);
            setTotalQuestions(0);
            toast.error(response.error || 'Failed to fetch questions');
        }
        setIsLoading(false);
    };

    useEffect(() => {
        fetchQuestions();
    }, [limit]);

    const handleCheckboxChange = (id: string, checked: boolean) => {
        setSelectedQuestions((prev) =>
            checked ? [...prev, id] : prev.filter((questionId) => questionId !== id)
        );
    };

    const handleBulkDelete = async () => {
        if (selectedQuestions.length === 0) {
            toast.error('Please select at least one question to delete');
            return;
        }
        const response = await deleteManyMockQuestions(selectedQuestions);
        if (response.success) {
            fetchQuestions(currentPage);
            setSelectedQuestions([]);
            toast.success(response.data.data.message || 'Selected questions deleted successfully!');
        } else {
            toast.error(response.error || 'Failed to delete selected questions');
        }
    };

    const onSubmit = async (data: QuestionBankFormInput) => {
        setIsSubmitting(true);
        setApiError(null);

        let response;
        if (editingQuestion) {
            response = await updateMockQuestionBank(editingQuestion.id.toString(), data);
        } else {
            response = await createMockQuestionBank(data);
        }
        if (response.success) {
            fetchQuestions(currentPage);
            setIsDialogOpen(false);
            reset({
                ...defaultFormValues,
            });
            setEditingQuestion(null);
            toast.success(editingQuestion ? 'Question updated successfully!' : 'Question created successfully!');
        } else {
            setApiError(response.error || 'Failed to save question');
            toast.error(response.error || 'Failed to save question');
        }
        setIsSubmitting(false);
    };

    const handleEdit = (question: MockQuestionBank) => {
        setEditingQuestion(question);
        const { question: q, optionOne, optionTwo, optionThree, optionFour, correctAnswer, quetionDate } = question;
        reset({
            question: q,
            optionOne,
            optionTwo,
            optionThree,
            optionFour,
            correctAnswer,
            quetionDate: quetionDate ? new Date(quetionDate) : new Date(),
        });
        setIsDialogOpen(true);
    };

    const handleDelete = async (id: string) => {
        const response = await deleteMockQuestionBank(id.toString());
        if (response.success) {
            fetchQuestions(currentPage);
            toast.success('Question deleted successfully!');
        } else {
            toast.error(response.error || 'Failed to delete question');
        }
    };

    const handleDialogOpenChange = (open: boolean) => {
        setIsDialogOpen(open);
        if (!open) {
            reset({
                ...defaultFormValues,
            });
            setEditingQuestion(null);
            setApiError(null);
        } else if (!editingQuestion) {
            reset({
                ...defaultFormValues,
            });
        }
    };

    const handleImportDialogOpenChange = (open: boolean) => {
        setIsImportDialogOpen(open);
        if (!open) {
            setSelectedFile(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    };

    const handleAddQuestion = () => {
        setEditingQuestion(null);
        reset({
            ...defaultFormValues,
        });
        setIsDialogOpen(true);
    };

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            const newPage = currentPage - 1;
            setCurrentPage(newPage);
            fetchQuestions(newPage);
        }
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            const newPage = currentPage + 1;
            setCurrentPage(newPage);
            fetchQuestions(newPage);
        }
    };

    const handleDownloadTemplate = () => {
        const link = document.createElement('a');
        link.href = '/Sample_Questions.xlsx';
        link.setAttribute('download', 'Sample_Questions.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) {
            toast.error('No file selected');
            return;
        }
        console.log('Selected file:', file.name, file.type); // Debugging
        const validExtensions = ['.xlsx', '.xls'];
        const isValidExtension = validExtensions.some((ext) =>
            file.name.toLowerCase().endsWith(ext)
        );
        if (!isValidExtension) {
            toast.error('Please select a valid Excel file (.xlsx or .xls)');
            return;
        }
        setSelectedFile(file);
        toast.success(`File selected: ${file.name}`);
    };

    const handleImportSubmit = async () => {
        console.log('Selected file in handleImportSubmit:', selectedFile); // Debugging
        if (!selectedFile) {
            toast.error('Please select an Excel file to import');
            return;
        }

        setIsSubmitting(true);
        const response = await importExcelQuestions(selectedFile);
        if (response.success) {
            fetchQuestions(1); // Refresh questions list
            setIsImportDialogOpen(false);
            setSelectedFile(null);
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
            toast.success(response.data.message || 'Questions imported successfully!');
        } else {
            toast.error(response.error || 'Failed to import questions');
        }
        setIsSubmitting(false);
    };

    const columns: ColumnDef<MockQuestionBank>[] = [
        {
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    checked={table.getIsAllRowsSelected()}
                    onCheckedChange={(value) => {
                        table.toggleAllRowsSelected(!!value);
                        setSelectedQuestions(
                            value ? questions.map((q) => q.id) : []
                        );
                    }}
                    aria-label="Select all questions"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={selectedQuestions.includes(row.original.id)}
                    onCheckedChange={(value) => {
                        handleCheckboxChange(row.original.id, !!value);
                        row.toggleSelected(!!value);
                    }}
                    aria-label="Select question"
                />
            ),
        },
        { accessorKey: 'question', header: 'Question' },
        { accessorKey: 'optionOne', header: 'Option 1' },
        { accessorKey: 'optionTwo', header: 'Option 2' },
        { accessorKey: 'optionThree', header: 'Option 3' },
        { accessorKey: 'optionFour', header: 'Option 4' },
        { accessorKey: 'correctAnswer', header: 'Correct Answer' },
        {
            accessorKey: 'questionDate',
            header: 'Question Date',
            cell: ({ row }) => {
                const date = row.original.quetionDate;
                return format(date, 'dd/MM/yyyy')
            }
        },
        { accessorKey: 'createdAt', header: 'Created At' },
        {
            id: 'actions',
            header: 'Actions',
            cell: ({ row }) => (
                <div className="flex gap-2">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(row.original)}
                        aria-label="Edit question"
                    >
                        <Pencil className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                        <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm" aria-label="Delete question">
                                <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                            <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete the question.
                                </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDelete(row.original.id)}>
                                    Delete
                                </AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>
                </div>
            ),
        },
    ];

    const table = useReactTable({
        data: questions,
        columns,
        getCoreRowModel: getCoreRowModel(),
    });

    return (
        <div className="p-4">
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold">Mock Exam Question Bank</h1>
                <div className="flex gap-3">
                    {/* Bulk Delete Button with Confirmation Dialog */}
                    <AlertDialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
                        <AlertDialogTrigger asChild>
                            <Button
                                variant="destructive"
                                onClick={() => {
                                    if (selectedQuestions.length === 0) {
                                        toast.error('Please select at least one question to delete');
                                    } else {
                                        setIsBulkDeleteDialogOpen(true);
                                    }
                                }}
                                disabled={isLoading}
                                aria-label="Delete selected questions"
                            >
                                Delete Bulk
                            </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                            <AlertDialogHeader>
                                <AlertDialogTitle>Confirm Bulk Deletion</AlertDialogTitle>
                                <AlertDialogDescription>
                                    Are you sure you want to delete {selectedQuestions.length} selected question(s)? This action cannot be undone.
                                </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                                <AlertDialogCancel>No</AlertDialogCancel>
                                <AlertDialogAction onClick={handleBulkDelete}>Yes</AlertDialogAction>
                            </AlertDialogFooter>
                        </AlertDialogContent>
                    </AlertDialog>

                    <Dialog open={isImportDialogOpen} onOpenChange={handleImportDialogOpenChange}>
                        <DialogTrigger asChild>
                            <Button aria-label="Import Excel file" className="flex items-center gap-2">
                                <Upload className="h-4 w-4" />
                                Import Excel
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>Import Questions from Excel</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                                <div>
                                    <Button
                                        variant="outline"
                                        onClick={handleDownloadTemplate}
                                        className="flex items-center gap-2"
                                    >
                                        <Download className="h-4 w-4" />
                                        Download Sample Excel
                                    </Button>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium mb-2">
                                        Upload Excel File
                                    </label>
                                    <Input
                                        type="file"
                                        accept=".xlsx,.xls"
                                        onChange={handleFileSelect}
                                        ref={fileInputRef}
                                        disabled={isSubmitting}
                                        aria-label="Upload Excel file"
                                    />
                                    {selectedFile && (
                                        <p className="text-sm text-gray-600 mt-2">
                                            Selected file: {selectedFile.name}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <DialogFooter>
                                <Button
                                    variant="outline"
                                    onClick={() => handleImportDialogOpenChange(false)}
                                    disabled={isSubmitting}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleImportSubmit}
                                    disabled={isSubmitting || !selectedFile}
                                >
                                    {isSubmitting ? 'Importing...' : 'Import'}
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
                        <DialogTrigger asChild>
                            <Button onClick={handleAddQuestion} aria-label="Add new question">
                                Add Question
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingQuestion ? 'Update Question' : 'Create Question'}
                                </DialogTitle>
                            </DialogHeader>
                            <>
                                {apiError && <p className="text-red-500 text-sm">{apiError}</p>}
                                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium">Question</label>
                                        <Input
                                            {...register('question')}
                                            placeholder="Enter question"
                                            disabled={isSubmitting}
                                            className="w-full"
                                            aria-label="Question input"
                                        />
                                        {errors.question && (
                                            <p className="text-red-500 text-sm">{errors.question.message}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-2">Question Date</label>
                                        <Controller
                                            name="quetionDate"
                                            control={control}
                                            render={({ field }) => (
                                                <div className="relative">
                                                    <input
                                                        type="date"
                                                        value={
                                                            field.value
                                                                ? new Date(field.value).toISOString().split('T')[0]
                                                                : ''
                                                        }
                                                        onChange={(e) => {
                                                            const dateValue = e.target.value;
                                                            field.onChange(dateValue ? new Date(dateValue) : null);
                                                        }}
                                                        min={new Date().toISOString().split('T')[0]}
                                                        disabled={isSubmitting}
                                                        className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
                                                    />
                                                    <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                                                </div>
                                            )}
                                        />
                                        {errors.quetionDate && (
                                            <p className="text-red-500 text-sm">{errors.quetionDate.message}</p>
                                        )}
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium">Option 1</label>
                                            <Input
                                                {...register('optionOne')}
                                                placeholder="Enter option 1"
                                                disabled={isSubmitting}
                                                aria-label="Option 1 input"
                                            />
                                            {errors.optionOne && (
                                                <p className="text-red-500 text-sm">{errors.optionOne.message}</p>
                                            )}
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium">Option 2</label>
                                            <Input
                                                {...register('optionTwo')}
                                                placeholder="Enter option 2"
                                                disabled={isSubmitting}
                                                aria-label="Option 2 input"
                                            />
                                            {errors.optionTwo && (
                                                <p className="text-red-500 text-sm">{errors.optionTwo.message}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium">Option 3</label>
                                            <Input
                                                {...register('optionThree')}
                                                placeholder="Enter option 3"
                                                disabled={isSubmitting}
                                                aria-label="Option 3 input"
                                            />
                                            {errors.optionThree && (
                                                <p className="text-red-500 text-sm">{errors.optionThree.message}</p>
                                            )}
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium">Option 4</label>
                                            <Input
                                                {...register('optionFour')}
                                                placeholder="Enter option 4"
                                                disabled={isSubmitting}
                                                aria-label="Option 4 input"
                                            />
                                            {errors.optionFour && (
                                                <p className="text-red-500 text-sm">{errors.optionFour.message}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div>
                                        <label className="block text-sm font-medium">Correct Answer</label>
                                        <Controller
                                            name="correctAnswer"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    onValueChange={field.onChange}
                                                    value={field.value}
                                                    disabled={isSubmitting}
                                                >
                                                    <SelectTrigger aria-label="Correct answer selector">
                                                        <SelectValue placeholder="Select correct answer" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="optionOne">Option 1</SelectItem>
                                                        <SelectItem value="optionTwo">Option 2</SelectItem>
                                                        <SelectItem value="optionThree">Option 3</SelectItem>
                                                        <SelectItem value="optionFour">Option 4</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            )}
                                        />
                                        {errors.correctAnswer && (
                                            <p className="text-red-500 text-sm">{errors.correctAnswer.message}</p>
                                        )}
                                    </div>


                                    <DialogFooter>
                                        <Button
                                            variant="outline"
                                            onClick={() => handleDialogOpenChange(false)}
                                            disabled={isSubmitting}
                                        >
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={isSubmitting}>
                                            {isSubmitting ? 'Saving...' : 'Save'}
                                        </Button>
                                    </DialogFooter>
                                </form>
                            </>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="text-center">
                                    Loading...
                                </TableCell>
                            </TableRow>
                        ) : table.getRowModel().rows.length > 0 ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={columns.length} className="text-center">
                                    No data available
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-gray-600">
                    {totalQuestions} entries
                </div>
                <div className="flex items-center gap-2">
                    <Select
                        value={limit.toString()}
                        onValueChange={(value) => {
                            setLimit(Number(value));
                            setCurrentPage(1);
                            fetchQuestions(1, value ? Number(value) : 10);
                        }}
                    >
                        <SelectTrigger className="w-[100px]" aria-label="Select number of questions per page">
                            <SelectValue placeholder="Select limit" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="20">20</SelectItem>
                            <SelectItem value="25">25</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePreviousPage}
                        disabled={currentPage === 1}
                        aria-label="Previous page"
                    >
                        <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <span className="text-sm">
                        Page {currentPage} of {totalPages}
                    </span>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleNextPage}
                        disabled={currentPage === totalPages}
                        aria-label="Next page"
                    >
                        <ChevronRight className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </div>
    );
}