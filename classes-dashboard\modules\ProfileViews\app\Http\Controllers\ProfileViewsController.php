<?php

namespace ProfileViews\Http\Controllers;

use App\Http\Controllers\Controller;
use ProfileViews\Repositories\ProfileViewsRepository;
use ProfileViews\Http\Requests\CreateProfileViewsRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfileViewsController extends Controller
{
    protected $schoolProfileViewsRepository;

    public function __construct(ProfileViewsRepository $schoolProfileViewsRepository)
    {
        $this->middleware('permission:read profileview', ['only' => ['index', 'getClassViewers']]);
        $this->middleware('permission:create profileview', ['only' => ['store']]);
        $this->schoolProfileViewsRepository = $schoolProfileViewsRepository;
    }

    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->schoolProfileViewsRepository->getAll($request);
            return $this->schoolProfileViewsRepository->getDatatable($list);
        }

        return view('ProfileViews::index');
    }
}
