<?php

namespace HomeWork\Models;

use Illuminate\Database\Eloquent\Model;
use Classroom\Models\Classroom;
use Subject\Models\Subject;


class HomeWork extends Model
{
    public $table = 'homeworks';
    protected $fillable = [
        'class_uuid',
        'classroom_id',
        'subject_id',
        'homework_date',
        'title',
        'description',
    ];

    public function classroom()
    {
        return $this->belongsTo(Classroom::class, 'classroom_id');
    }
    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }
}
