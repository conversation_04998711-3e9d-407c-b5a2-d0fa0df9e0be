import { axiosInstance } from '@/lib/axios';

export const addUestCoinTranscation = async (
  data: any
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/uwhizCoinTransction/add", data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to log transction of coins in mock exam: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const updateUestCoins = async (data: {
  modelId: string;
  modelType: string;
  coins: number;
}): Promise<any> => {
  try {
    const response = await axiosInstance.post("/uwhizCoinTransction/update", data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to update coins: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};