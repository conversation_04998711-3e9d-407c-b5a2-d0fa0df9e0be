<?php

namespace Blogs\Http\Controllers;

use Blogs\Http\Requests\CreateBlogsRequest;
use Blogs\Repositories\BlogsRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BlogsController extends Controller
{

    protected $blogsRepository;
    public function __construct(BlogsRepository $blogsRepository)
    {
        $this->blogsRepository = $blogsRepository;
    }
    
    public function index(Request $request)
    {
        $list = $this->blogsRepository->getAll($request);
        if (request()->ajax()) {
            return $this->blogsRepository->getDatatable($list);
        }
        return view('Blogs::index', compact('list'));
    }

    public function create()
    {
        return view('Blogs::create');
    }

    public function store(CreateBlogsRequest $request)
    {
       $this->blogsRepository->createBlogs($request);
       return response()->json(['success' => 'Blogs Created Successfully!!']);
    }

    public function edit($id)
    {
        $data = $this->blogsRepository->getBlogsById($id);
        return view('Blogs::edit', compact('data'));
    }

    public function update(CreateBlogsRequest $request, $id)
    {
        $this->blogsRepository->updateBlogs($request, $id);
        return response()->json(['success' => 'Blogs Updated successfully!!']);
    }

    public function destroy($id)
    {
        $blogs = $this->blogsRepository->getBlogsById($id);
        $blogs->delete();
        return response()->json(['success' => 'Blogs deleted successfully!!']);
    }
}
