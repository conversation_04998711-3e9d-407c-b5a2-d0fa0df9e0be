<div class="row">
    
     <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('homework_date', 'Homework Date', ['class' => 'form-label']) !!}
            {!! Form::date('homework_date', null, ['class' => 'form-control datepicker', 'readonly']) !!}
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label>Classroom *</label>
            <select class="form-control select2" name="classroom_id" id="classroom_id">
                @foreach ($classroom as $class)
                    <option {{ isset($data) && $data->classroom_id == $class->id ? 'selected' : '' }}
                        value="{{ $class->id }}">{{ $class->class_name }} ({{ $class->department_name }})</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label>Subject *</label>
            <select class="form-control select2" name="subject_id" id="subject_id">
                @foreach ($subject as $subject)
                    <option {{ isset($data) && $data->subject_id == $subject->id ? 'selected' : '' }}
                        value="{{ $subject->id }}">{{ $subject->subject_name }} ({{ $subject->class_name }})</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('title', 'Title *', ['class' => 'form-label']) !!}
            {!! Form::text('title', null, [
                'class' => 'form-control',
                'id' => 'title',
                'placeholder' => 'Enter Title',
            ]) !!}
        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::label('description', 'Homework Description *', ['class' => 'form-label']) !!}
            {!! Form::textarea('description', null, [
                'class' => 'form-control editor',
                'placeholder' => 'Enter Homework Description',
            ]) !!}
        </div>
    </div>

    {!! Form::submit('Submit', ['id' => 'saveHomeWorks', 'class' => 'btn btn-primary']) !!}
    <button data-dismiss="modal" class="btn btn-secondary ml-2">Cancel </button>
</div>
