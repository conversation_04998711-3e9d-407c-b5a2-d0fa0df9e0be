"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Reviews } from "@/lib/types";
import { getReviews, deleteReview } from "@/services/reviewsApi";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/app-components/dataTable";
import { Button } from "@/components/ui/button";
import { Trash2, Star } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";

import Pagination from "@/app-components/pagination";
import ConfirmDialog from "@/app-components/ConfirmDialog";

const PAGE_SIZE = 10;

const ReviewsPage = () => {
  const [reviews, setReviews] = useState<Reviews[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteReviewId, setDeleteReviewId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchReviews = useCallback(async (page: number) => {
    setIsLoading(true);
    try {
      const resp = await getReviews(page, PAGE_SIZE);
      setReviews(resp.testimonials);
      setTotalPages(resp.totalPages);
    } catch (e) {
      console.error(e);
      toast.error("Failed to fetch reviews");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchReviews(currentPage);
  }, [fetchReviews, currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchReviews(page);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteReviewId) return;
    setIsDeleting(true);
    try {
      await deleteReview(deleteReviewId);
      toast.success("Review deleted");
      fetchReviews(currentPage);
    } catch (e: any) {
      toast.error(e.message || "Failed to delete review");
    } finally {
      setDeleteReviewId(null);
      setIsDeleting(false);
    }
  };

  const columns: ColumnDef<Reviews>[] = [
    {
      accessorKey: "studentName",
      header: "User",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "rating",
      header: "Rating",
      cell: ({ row }) => (
        <div className="flex gap-1">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              className={`w-4 h-4 ${
                i < row.original.rating
                  ? "fill-yellow-500 text-yellow-500"
                  : "text-gray-300"
              }`}
            />
          ))}
        </div>
      ),
    },
    {
      accessorKey: "message",
      header: "Review",
      cell: ({ row }) => {
        const msg = row.original.message;
        // optional truncate
        return msg.length > 60 ? msg.slice(0, 57) + "..." : msg;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Date",
      cell: ({ row }) =>
        format(new Date(row.original.createdAt), "dd MMM yyyy"),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setDeleteReviewId(row.original.id)}
            className="text-red-600 hover:text-red-800 hover:bg-red-100"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Reviews</h1>
      </div>

      <DataTable
        columns={columns}
        data={reviews}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${reviews.length} entries`}
      />

      <ConfirmDialog
        open={!!deleteReviewId}
        setOpen={(val) => {
          if (!val) setDeleteReviewId(null);
        }}
        title="Confirm Deletion"
        description="This will permanently delete the review."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDeleteConfirm}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default ReviewsPage;
