"use client";
import React, { useEffect, useState } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { DataTable } from "@/app-components/dataTable";
import { ColumnDef } from "@tanstack/react-table";
import {
  getStudentsByClassId,
  getUniqueYears,
} from "@/services/classes-student";
import { ClassesStudent } from "@/lib/types";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import Pagination from "@/app-components/pagination";

export default function StudentsList() {
  const params = useParams();
  const searchParams = useSearchParams();
  const classId = params?.id as string;
  const className = searchParams.get("name") || "N/A";
  const [students, setStudents] = useState<ClassesStudent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedYear, setSelectedYear] = useState("all");
  const [years, setYears] = useState<string[]>([]);

  const columns: ColumnDef<ClassesStudent>[] = [
    {
      accessorKey: "student_full_name",
      header: "Full Name",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "date_of_birth",
      header: "Date of Birth",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "contact_no",
      header: "Contact",
      cell: (info) => info.getValue() || "N/A",
    },
  ];

  const getStudents = async (
    page: number = 1,
    search: string = "",
    year: string = ""
  ) => {
    try {
      if (!classId) {
        setStudents([]);
        setTotalPages(1);
        setTotalItems(0);
        return;
      }
      setIsLoading(true);
      const yearParam = year === "all" ? "" : year;
      const response = await getStudentsByClassId(
        classId,
        page,
        search,
        yearParam
      );
      setStudents(response.data);
      setTotalPages(response.last_page);
      setTotalItems(response.total);
      setCurrentPage(response.current_page);
    } catch (error) {
      setStudents([]);
      setTotalPages(1);
      setTotalItems(0);
      console.error("Error Fetching Student", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getYears = async () => {
    try {
      const uniqueYears = await getUniqueYears(classId);
      setYears(uniqueYears);
    } catch (error) {
      console.error("Error Fetching Years", error);
    }
  };

  useEffect(() => {
    if (classId) {
      getYears();
      getStudents(currentPage, searchTerm, selectedYear);
    }
  }, [classId]);

  useEffect(() => {
    getStudents(currentPage, searchTerm, selectedYear);
  }, [currentPage, selectedYear]);

  const handleSearch = () => {
    setCurrentPage(1);
    getStudents(1, searchTerm, selectedYear);
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleYearChange = (value: string) => {
    setSelectedYear(value);
    setCurrentPage(1);
  };

  return (
    <div className="p-4">
      <div className="text-xl  mb-2 flex">
        <span>Student List of</span>
        {className !== "N/A" && (
          <span className="ml-2 font-bold">{className}</span>
        )}
      </div>

      <div className="mb-4 flex flex-row items-center gap-2">
        <Input
          value={searchTerm}
          onChange={handleSearchChange}
          onKeyDown={handleSearchKeyDown}
          placeholder="Search by name or email..."
          className="max-w-sm"
        />
        <Select value={selectedYear} onValueChange={handleYearChange}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select Year" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {years.map((year) => (
              <SelectItem key={year} value={year}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={handleSearch}>Search</Button>
      </div>
      {isLoading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-4">Loading...</span>
        </div>
      ) : students.length === 0 ? (
        <p>No students found.</p>
      ) : (
        <>
          <DataTable columns={columns} data={students} isLoading={isLoading} />

          <Pagination
            page={totalItems}
            totalPages={totalPages}
            setPage={setTotalPages}
            entriesText={`${students.length} entries`}
          />
        </>
      )}
    </div>
  );
}
