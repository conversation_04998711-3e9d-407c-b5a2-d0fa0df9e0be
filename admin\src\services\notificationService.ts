import { axiosInstance } from '@/lib/axios';

export interface AdminNotification {
  id: string;
  userId: string;
  userType: 'ADMIN';
  type: 'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' |
        'NEW_EXAM_APPLICATION';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdminNotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface AdminNotificationResponse {
  notifications: AdminNotification[];
  pagination: AdminNotificationPagination;
}

// Admin notification functions
export const getAdminNotifications = async (page: number = 1, limit: number = 10): Promise<AdminNotificationResponse> => {
  try {
    const response = await axiosInstance.get(`/notifications/admin?page=${page}&limit=${limit}`);
    const data = response.data.data;

    // Handle different response formats
    if (Array.isArray(data)) {
      // Old format - direct array
      return {
        notifications: data,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalCount: data.length,
          limit: data.length,
          hasNextPage: false,
          hasPrevPage: false
        }
      };
    }

    // New format - with pagination
    return data || {
      notifications: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        limit: 10,
        hasNextPage: false,
        hasPrevPage: false
      }
    };
  } catch {
    return {
      notifications: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        limit: 10,
        hasNextPage: false,
        hasPrevPage: false
      }
    };
  }
};



export const getAdminUnreadCount = async (): Promise<number> => {
  const response = await axiosInstance.get('/notifications/admin/count');
  return response.data.data.count || 0;
};

export const markAdminNotificationAsRead = async (notificationId: string) => {
  const response = await axiosInstance.post(`/notifications/admin/mark-read/${notificationId}`);
  return response.data;
};

export const markAllAdminNotificationsAsRead = async () => {
  const response = await axiosInstance.post('/notifications/admin/mark-all-read');
  return response.data;
};

export const deleteAllAdminNotifications = async () => {
  const response = await axiosInstance.delete('/notifications/admin/delete-all');
  return response.data;
};

// Helper function to get notification action URL based on type and data
export const getNotificationActionUrl = (notification: AdminNotification): string => {
  switch (notification.type) {
    case 'ADMIN_NEW_STUDENT_REGISTRATION':
      return `/student-details`;

    case 'ADMIN_NEW_CLASS_REGISTRATION':
      return `/dashboard`;

    case 'ADMIN_PROFILE_REVIEW_REQUIRED':
      if (notification.data?.studentId) {
        return `/student-profile/${notification.data.studentId}`;
      } else if (notification.data?.classId) {
        return `/classes-details/${notification.data.classId}`;
      }
      return `/student-details`;

    case 'ADMIN_CONTENT_REVIEW_REQUIRED':
      if (notification.data?.blogId) {
        return `/blog`;
      } else if (notification.data?.testimonialId) {
        return `/testimonials`;
      } else if (notification.data?.type === 'blog') {
        return `/blog`;
      } else if (notification.data?.type === 'testimonial') {
        return `/testimonials`;
      }
      return `/blog`;

    case 'NEW_EXAM_APPLICATION':
      if (notification.data?.redirectUrl) {
        return notification.data.redirectUrl;
      }
      if (notification.data?.examId) {
        return `/exam-applicant/${notification.data.examId}`;
      }
      return `/dashboard`;

    default:
      return '/dashboard';
  }
};

// Helper function to get notification icon based on type
export const getNotificationIcon = (type: AdminNotification['type']): string => {
  switch (type) {
    case 'ADMIN_NEW_STUDENT_REGISTRATION':
      return '👨‍🎓';
    case 'ADMIN_NEW_CLASS_REGISTRATION':
      return '🏫';
    case 'ADMIN_PROFILE_REVIEW_REQUIRED':
      return '📋';
    case 'ADMIN_CONTENT_REVIEW_REQUIRED':
      return '📝';
    case 'NEW_EXAM_APPLICATION':
      return '📝';
    default:
      return '🔔';
  }
};


