<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::model($data, ['route' => ['homeWork.update', $data->id],'id'=>'edithomeWorks_form']) !!}
                        @include('HomeWork::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('HomeWork\Http\Requests\CreateHomeWorkRequest', '#edithomeWorks_form') !!}
<script>
     var editHomeWorkRoute = {
        update: "{{ route('homeWork.update', $data->id) }}",
    };
    initializeCKEditor($(".editor")[0]);
</script>

<script src="{{ asset(mix('js/page-level-js/HomeWork/edit.js')) }}"></script>