<?php

namespace Testimonial\Http\Controllers;

use Testimonial\Http\Requests\CreateTestimonialRequest;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Testimonial\Repositories\TestimonialRepository;

class TestimonialController extends Controller
{

    protected $testimonialRepository;
    public function __construct(TestimonialRepository $testimonialRepository)
    {
        $this->testimonialRepository = $testimonialRepository;
    }
    
    public function index(Request $request)
    {
        $list = $this->testimonialRepository->getAll($request);
        if (request()->ajax()) {
            return $this->testimonialRepository->getDatatable($list);
        }
        return view('Testimonial::index', compact('list'));
    }

    public function create()
    {
        return view('Testimonial::create');
    }

    public function store(CreateTestimonialRequest $request)
    {
       $this->testimonialRepository->createTestimonial($request);
       return response()->json(['success' => 'Testimonial Created Successfully!!']);
    }

    public function edit($id)
    {
        $data = $this->testimonialRepository->getTestimonialById($id);
        return view('Testimonial::edit', compact('data'));
    }

    public function update(CreateTestimonialRequest $request, $id)
    {
        $this->testimonialRepository->updateTestimonial($request, $id);
        return response()->json(['success' => 'Testimonial Updated successfully!!']);
    }

    public function destroy($id)
    {
        $testimonial = $this->testimonialRepository->getTestimonialById($id);
        $testimonial->delete();
        return response()->json(['success' => 'Testimonial deleted successfully!!']);
    }
}
