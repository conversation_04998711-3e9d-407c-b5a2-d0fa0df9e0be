@extends('layouts.app')
@section('content')
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12 main-title-flex">
                <h1>Profile Views</h1>
            </div>
        </div>
    </div>
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="tab-content" id="profileViewsTabContent">
                            <!-- Profile Views Tab -->
                            <div class="tab-pane fade show active" id="profile-views" role="tabpanel" aria-labelledby="profile-views-tab">
                                <table id="profileviews_table" class="table display table-striped table-borderless dt-responsive">
                                    <thead>
                                        <tr>
                                            <th>Class Name</th>
                                            <th>Student Name</th>
                                            <th>Student Email</th>
                                            <th>Viewed At</th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr class="search-row">
                                            <th>Class Name</th>
                                            <th>Student Name</th>
                                            <th>Student Email</th>
                                            <th>Viewed At</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
@section('scripts')
<script>
    var profileviewsRoute = {
        index: "{{ route('profileviews') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/profileviews/index.js')) }}"></script>
@endsection