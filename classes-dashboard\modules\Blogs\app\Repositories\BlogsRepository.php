<?php

namespace Blogs\Repositories;

use Blogs\Models\Blogs;
use Blogs\Interfaces\BlogsInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class BlogsRepository implements BlogsInterface
{
    protected $blogs;
    function __construct(Blogs $blogs)
    {
        $this->blogs = $blogs;
    }

    public function getAll($request)
    {
        $blogs = $this->blogs::where('classId', Auth::id());
    
        searchColumn($request->input('columns'), $blogs);
        orderColumn($request, $blogs, 'Blog.id');

        return $blogs;
    }

    public function getDatatable($list)
    {
        return datatables()->of($list)
        ->addColumn('blogs', function ($data) {
            return $data->blogs;
        })
        ->addColumn('createdAt', function ($data) {
            return $data->createdAt;
        })
        ->addColumn('blogTitle', function ($data) {
            return $data->blogTitle;
        })
        ->addColumn('status', function ($data) {
            return status_color($data->status);
        })
        ->addColumn('action', function ($data) {
            $button = "";
            if ($data->status != "APPROVED") {
                $button .= '<a href="' . route('blogs.edit', $data->id) . '" class="btn" title="Edit" data-editblogsid="' . $data->id . '"><i class="fa fa-edit"></i></a>';
                $button .= '<button type="button" class="deleteBlogsEntry btn" title="Delete" data-deleteblogsid="' . $data->id . '"><i class="fa fa-trash"></i></button>';
            }
            return $button;
        })->rawColumns(['action', 'status'])
        ->make(true);
    }

    public function getBlogsById($id)
    {
        return $this->blogs::Find($id);
    }
}
