"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  createsubjectPrefrence,
  getSubjectPrefrence,
  deleteSubjectPrefrence,
  getSubject,
} from "@/services/uwhizSubjectPrefrenceApi";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Plus, Trash2Icon } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
// import { DataTable } from "@/components/PriceRankTable";
import { DataTable } from "@/app-components/dataTable";
import ConfirmDialog from "@/app-components/ConfirmDialog";
import Pagination from "@/app-components/pagination";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const formSchema = z.object({
  subject: z.string().min(1, "Subject is required"),
});

interface SubjectPreference {
  subject: string;
  weightage: number;
  id: string;
}

interface Subject {
  id: number;
  value: string;
  categoryId: number;
}

interface FormValues {
  subject: string;
}

interface SubjectPreferenceManagerProps {
  examId: string;
}

export default function SubjectPreferenceManager({
  examId,
}: SubjectPreferenceManagerProps) {
  const [subjectPreferences, setSubjectPreferences] = useState<
    SubjectPreference[]
  >([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  const paginatedData = subjectPreferences.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const totalItems = subjectPreferences.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: "",
    },
  });

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);

      const subjectResponse = await getSubject();
      if (subjectResponse.success) {
        setSubjects(subjectResponse.data?.details || []);
      } else {
        toast.error(subjectResponse.error || "Failed to fetch subjects");
      }

      const preferenceResponse = await getSubjectPrefrence(
        examId ? Number(examId) : 0
      );
      if (preferenceResponse.success) {
        setSubjectPreferences(
          Array.isArray(preferenceResponse.data.data)
            ? preferenceResponse.data.data
            : []
        );
      } else {
        toast.error(
          preferenceResponse.error || "Failed to fetch subject preferences"
        );
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch data");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (examId) {
      fetchData();
    }
  }, [examId, fetchData]);

  const onSubmit = async (values: FormValues) => {
    try {
      const response = await createsubjectPrefrence({
        examId: Number(examId),
        subject: values.subject,
        weightage: 0,
      });
      if (response.success) {
        toast.success("Subject preference added successfully");
        setIsDialogOpen(false);
        form.reset();
        fetchData();
      } else {
        toast.error(response.error || "Failed to add subject preference");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to add subject preference");
    }
  };

  const handleDelete = (id: string) => {
    setDeleteId(id);
  };

  const confirmDelete = async () => {
    try {
      if (deleteId) {
        const response = await deleteSubjectPrefrence(deleteId);
        if (response.success) {
          toast.success("Subject preference deleted successfully");
          setDeleteId(null);
          fetchData();
        } else {
          toast.error(response.error || "Failed to delete subject preference");
        }
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to delete subject preference");
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
    form.reset();
  };

  const columns: ColumnDef<SubjectPreference>[] = [
    {
      accessorKey: "subject",
      header: "Subject",
    },
    {
      accessorKey: "weightage",
      header: "Weightage (%)",
      cell: ({ row }) => <div>{row.original.weightage.toFixed(2)}%</div>,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2Icon className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Subject Preferences</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Subject Preference
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Subject Preference</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a subject" />
                          </SelectTrigger>
                          <SelectContent>
                            {subjects.map((subject) => (
                              <SelectItem
                                key={subject.id}
                                value={subject.value}
                              >
                                {subject.value}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Save</Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <div>Loading...</div>
      ) : (
        <DataTable
          data={paginatedData}
          columns={columns}
          isLoading={isLoading}
        />
      )}

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${totalItems} entries`}
      />

      <ConfirmDialog
        open={!!deleteId}
        setOpen={(val) => {
          if (!val) setDeleteId(null);
        }}
        title="Are you sure?"
        description="This action cannot be undone. This will permanently delete the subject preference."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        isLoading={isLoading}
      />
    </div>
  );
}
