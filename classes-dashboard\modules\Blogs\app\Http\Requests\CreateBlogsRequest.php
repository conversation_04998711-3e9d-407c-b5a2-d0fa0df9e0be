<?php

namespace Blogs\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateBlogsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'blogTitle' => 'required|string|max:250',
            'blogDescription' => 'required|string',
            'blogImage' => 'required|image|mimes:jpeg,png,jpg,webp,gif|max:2048',
        ];
    }
}
