<?php

namespace Admission\Http\Controllers;

use Admission\Http\Requests\CreateLeavingCertificateRequest;
use Admission\Http\Requests\CreateStudentDetailsRequest;
use Admission\Repositories\AdmissionRepository;
use Fees\Repositories\ClassroomWiseFeeRepository;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use StudentAttendance\Repositories\AttendanceRepository;
use Years\Repositories\YearsRepository;

class AdmissionController extends Controller
{
    protected $admissionRepository;

    protected $yearsRepository;
    protected $classroomWiseFeeRepository;
    protected $attendanceRepository;

    public function __construct(
        AdmissionRepository $admissionRepository,
        YearsRepository $yearsRepository,
        ClassroomWiseFeeRepository $classroomWiseFeeRepository,
        AttendanceRepository $attendanceRepository
    ) {
        $this->middleware('permission:read student', ['only' => ['index', 'show']]);
        $this->middleware('permission:create student', ['only' => [
            'create',
            'storeStudentDetails',
            'storeStudentParentsDetails',
            'storeStudentSiblingsDetails',
            'storeStudentHealthDetails',
            'storeStudentPastDetails'
        ]]);
        $this->middleware('permission:update student', ['only' => [
            'edit',
            'storeStudentDetails',
            'storeStudentParentsDetails',
            'storeStudentSiblingsDetails',
            'storeStudentHealthDetails',
            'storeStudentPastDetails'
        ]]);
        $this->middleware('permission:manage student exit', ['only' => ['generateLeavingCertificate']]);
        $this->admissionRepository = $admissionRepository;
        $this->yearsRepository = $yearsRepository;
        $this->classroomWiseFeeRepository = $classroomWiseFeeRepository;
        $this->attendanceRepository = $attendanceRepository;
    }

    public function index(Request $request)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();

        if (request()->ajax()) {
            $list = $this->admissionRepository->getAll($request);
            return $this->admissionRepository->getDatatable($list);
        }
        return view('Admission::index', compact("department", "years"));
    }

    public function create()
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        return view('Admission::create', compact('department', 'years'));
    }

    public function storeStudentDetails(CreateStudentDetailsRequest $request)
    {

        $id = $this->admissionRepository->storeStudentDetails($request->all());
        return response()->json(['success' => 'Student Details Added Successfully!!', 'id' => $id]);
    }

    public function edit($id)
    {
        $department = departmentForStudentPortal();
        $years = $this->yearsRepository->getAll();
        $data = $this->admissionRepository->findByid($id);
        return view('Admission::create', compact('data', 'department', 'years'));
    }

    public function show($id)
    {
        $year = geActiveYearName();
        $dateData['startDate'] = $year->start_date;
        $dateData['endDate'] = $year->end_date;

        $data = $this->admissionRepository->findByid($id);
        $attendance = $this->attendanceRepository->getAttendanceForAPI($dateData, $id);
        ksort($attendance);
        return view('Admission::show', compact('data', 'attendance'));
    }

    public function destroy($id)
    {
        $this->admissionRepository->findByid($id)->delete();
        return response()->json(['success' => 'Student Deleted Successfully']);
    }

    public function searchStudent(Request $request)
    {
        $data = $this->admissionRepository->searchStudent($request);
        return $data;
    }

    public function activeInactive($id)
    {
        $this->admissionRepository->activeInactive($id);
        return redirect()->back()->with('success', 'Student Status Updated successfully!!');
    }

    public function exportStudents(Request $request)
    {
        $students = $this->admissionRepository->getAll($request)->get();
        return commonExport($students, 'Admission::export', 'students');
    }

    public function getStudentsByClassroom(Request $request)
    {
        $students = $this->admissionRepository->getAllStudentByClassAndDepartment($request->all());
        return response()->json(array('students' => $students));
    }

    public function getAllStudentsRawAPI(Request $request)
    {
        $classUuid = $request->query('class_uuid');
        $search = $request->query('search');
        $yearName = $request->query('year_name');
        $students = $this->admissionRepository->getAllStudentsRaw($classUuid, $search, $yearName);
        return response()->json(['students' => $students]);
    }

    public function checkByMobile(Request $request)
    {
        $contactNo = $request->input('contact_no');

        $studentData = $this->admissionRepository->checkByMobile($contactNo);

        if (!$studentData['student']) {
            return response()->json(['error' => true, 'no_student' => true, 'message' => 'No student found with this mobile number']);
        }

        if ($studentData['academicInfo']) {
            return response()->json([
                'error' => true,
                'message' => 'Student is already active in another class. Please make the existing record inactive first.',
            ]);
        }

        $verifiedKey = "otp_verified_{$contactNo}_" . Auth::id();

        if (Cache::get($verifiedKey)) {
            return response()->json([
                'success' => true,
                'already_verified' => true,
                'message' => 'OTP already verified recently',
                'student' => $studentData['student'],
            ]);
        }

        $otp = rand(100000, 999999);

        Cache::put("otp_{$contactNo}_" . Auth::id(), $otp, now()->addMinutes(5));

        $throttleKey = "otp_throttle_{$contactNo}_" . Auth::id();
        if (Cache::has($throttleKey)) {
            return response()->json([
                'error' => true,
                'message' => 'Please wait 1 min before requesting another OTP.',
            ]);
        }
        Cache::put($throttleKey, true, now()->addSeconds(60));

        $user = $studentData['student'];
        $firstName = $user->firstName ?? 'Student';

        $message = "Hi {$firstName}, Please use the code {$otp} to log in to Uest EdTech. For your security, please do not share this code with anyone. Thank you!";

        try {
            Http::get(env('SHREE_TRIPADA_API_URL'), [
                'auth_key'   => env('SHREE_TRIPADA_AUTH_KEY'),
                'mobiles'    => "91{$contactNo}",
                'templateid' => env('SHREE_TRIPADA_TEMPLATE_ID'),
                'message'    => $message,
                'sender'     => env('SHREE_TRIPADA_SENDER'),
                'route'      => '4',
            ]);

            return response()->json([
                'success' => true,
                'otp_sent' => true,
                'message' => 'OTP sent successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => true,
                'message' => 'Failed to send OTP. Please try again later.',
                'exception' => $e->getMessage(),
            ], 500);
        }
    }

    public function verifyOtp(Request $request)
    {
        $contact = $request->input('contact_no');
        $otpInput = $request->input('otp');

        $cachedOtp = Cache::get("otp_{$contact}_" . Auth::id());

        if (!$cachedOtp || $cachedOtp != $otpInput) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired OTP',
            ], 401);
        }

        Cache::forget("otp_{$contact}_" . Auth::id());
        Cache::put("otp_verified_{$contact}_" . Auth::id(), true, now()->addMinutes(10));
        $studentData = $this->admissionRepository->checkByMobile($contact);

        return response()->json([
            'success' => true,
            'student' => $studentData['student'],
            'academicInfo' => $studentData['academicInfo'],
        ]);
    }
}
