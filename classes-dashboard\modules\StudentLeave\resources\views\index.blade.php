@extends('layouts.app')

@section('content')
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-9">
                    <h1>Student Leave Approval</h1>
                </div>
            </div>
        </div>
    </div>
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="box-title popup-title m-0">Filter Leave Data</h3>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>From Date</b></label>
                                        <input class="form-control" placeholder="Select From Date" name="start_date"
                                            type="text" readonly id="start_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>To Date</b></label>
                                        <input class="form-control" placeholder="Select To Date" name="end_date"
                                            type="text" readonly id="end_date">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><b>Status</b></label>
                                        <select class="form-control select2" id="status" aria-invalid="false">
                                            <option value="">All</option>
                                            <option value="{{ config('constants.STATUS.PENDING') }}">
                                                {{ config('constants.STATUS.PENDING') }}
                                            </option>
                                            <option value="{{ config('constants.STATUS.APPROVED') }}">
                                                {{ config('constants.STATUS.APPROVED') }}
                                            </option>
                                            <option value="{{ config('constants.STATUS.REJECT') }}">
                                                {{ config('constants.STATUS.REJECT') }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button id="filterleaveapptable" class="btn btn-primary filter-btn">Filter</button>
                                        <button id="filterreset" class="btn btn-secondary filter-btn">Reset</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="generate-buttons">
                                @can('export leave data')
                                    <button class="btn btn-dark exportData"><i class="fa fa-file-excel"></i> Export</button>
                                @endcan
                            </div>
                            <div class="btn-extra">
                                <button value="{{ config('constants.STATUS.APPROVED') }}" disabled title="Approve"
                                    class="btn bg-success multipleapproval"><i class="nav-icon fa fa-thumbs-up"></i> Approve
                                    All</button>
                                <button value="{{ config('constants.STATUS.REJECT') }}" disabled title="Reject"
                                    class="btn bg-danger multipleapproval"><i class="nav-icon fa fa-thumbs-up"></i> Reject
                                    All</button>
                            </div>
                            <table id="studentleave_table"
                                class="table display table-striped table-borderless dt-responsive">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="custom-control custom-checkbox">
                                                <input class="checkbox-m custom-control-input custom-control-input-success"
                                                    type="checkbox" id="request-select-all">
                                                <label for="request-select-all" class="custom-control-label"></label>
                                            </div>
                                        </th>
                                        <th class="desktop tablet mobile">Action</th>
                                        <th class="desktop tablet mobile">Student Name</th>
                                        <th class="desktop tablet">Class Name</th>
                                        <th class="desktop tablet mobile">Leave Date</th>
                                        <th class="desktop tablet">Reason</th>
                                        <th class="desktop tablet">Status</th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr class="search-row">
                                        <th></th>
                                        <th class="desktop tablet mobile">Action</th>
                                        <th class="desktop tablet mobile">Student Name</th>
                                        <th class="desktop tablet">Class Name</th>
                                        <th class="desktop tablet mobile">Leave Date</th>
                                        <th class="desktop tablet">Reason</th>
                                        <th class="desktop tablet">Status</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('scripts')
    <script>
        var leaveRoute = {
            index: "{{ route('studentleave.index') }}",
            status: "{{ route('studentleave.status', ':leaveid') }}",
            multipleapprove: "{{ route('studentleave.multistatus') }}",
            export: "{{ route('export-leaves') }}",
        };
    </script>
    <script src="{{ asset(mix('js/page-level-js/studentleave/index.js')) }}"></script>
@endsection
