<?php

namespace StudentLeave\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use StudentLeave\Repositories\StudentLeaveRepository;

class StudentLeaveController extends Controller
{
    protected $studentLeaveRepository;

    public function __construct(StudentLeaveRepository $studentLeaveRepository)
    {
        $this->studentLeaveRepository = $studentLeaveRepository;
    }

    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->studentLeaveRepository->getAll($request);
            return $this->studentLeaveRepository->getDatatable($list);
        }
        return view('StudentLeave::index');
    }

    public function leave_approve($id, Request $request)
    {
        $this->studentLeaveRepository->leaveapprove($request, $id);
        return response()->json(['success' => 'Leave Status Updated Successfully']);
    }

    public function leave_multipleapprove(Request $request)
    {
        $this->studentLeaveRepository->multipleapprove($request);
        return response()->json(['success' => 'Leave Status Updated Successfully!!']);
    }

    public function exportLeaves(Request $request)
    {
        $leaves = $this->studentLeaveRepository->getAll($request)->get();
        return commonExport($leaves, 'StudentLeave::export', 'Student_Leaves');
    }
}