<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class CreateStudentFeesView extends Migration
{
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS student_fees_view");

        DB::statement("
            CREATE VIEW student_fees_view AS
            SELECT 
                sai.id,
                s.\"firstName\" AS first_name,
                s.\"middleName\" AS middle_name,
                s.\"lastName\" AS last_name,
                c.id AS class_id,
                c.class_name,
                y.year_name AS year_name,
                y.id AS year_id,
                cfd.id AS fee_detail_id,
                cfd.amount,
                fc.category_name AS fees_category,
                fc.id AS fees_category_id,
                cfd2.lock AS fee_locked
            FROM 
                \"Student\" s
            LEFT JOIN student_academic_info sai ON s.id = sai.student_id
            LEFT JOIN years y ON sai.year = y.id
            LEFT JOIN department d ON sai.department = d.id
            LEFT JOIN classrooms c ON sai.classroom = c.id
            LEFT JOIN classroom_fees_details cfd2 ON c.id = cfd2.classroom_id AND cfd2.year_id = sai.year
            LEFT JOIN classroom_category_fees_details cfd ON cfd2.id = cfd.classroom_fees_details_id
            LEFT JOIN fees_category fc ON fc.id = cfd.category_id AND fc.deleted_at IS NULL
        ");
    }

    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS student_fees_view");
    }
}