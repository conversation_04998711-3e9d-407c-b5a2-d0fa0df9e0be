<section class="content">
    <div class="row">
        <div class="col-md-12">
            <div class="card card-default">
                <div class="card-body">
                    {!! Form::open(['route' => 'classWork.store', 'enctype' => 'multipart/form-data' ,'id'=>'createclassWork_form']) !!}
                    @include('ClassWork::fields')
                    {!! Form::close() !!}
                </div>
            </div>
        </div>
        <!-- /.card -->
    </div>
    </div>
</section>
{!! JsValidator::formRequest('ClassWork\Http\Requests\CreateClassWorkRequest', '#createclassWork_form') !!}
<script>
     var createClassWorkRoute = {
        store: "{{ route('classWork.store') }}",
    };
    initializeCKEditor($(".editor")[0]);
</script>

<script src="{{ asset(mix('js/page-level-js/ClassWork/create.js')) }}"></script>