function showLoader() {
    $(".page-loader").show();
}

function hideLoader() {
    $(".page-loader").hide();
}

$(document).ready(function () {
    if ($(".filter-btn").length > 0) {
        if ($(".generate-buttons").length > 0) {
            $(".filter-btn")
                .parent()
                .parent()
                .parent()
                .parent()
                .parent()
                .hide();
            let button = $(
                '<button class="btn btn-primary" id="showFilter"><i class="fa fa-filter"></i>&nbsp; Filter</button>'
            );
            $(".generate-buttons").append(button);
        }
    }

    $("#showFilter").click(function () {
        $(".filter-btn")
            .parent()
            .parent()
            .parent()
            .parent()
            .parent()
            .slideToggle(500);
    });
});

toastr.options = {
    closeButton: true,
};

function ajaxHandler(form, url, method, formid, subbtn, popupid, tableid) {
    showLoader();
    $.ajax({
        url: url,
        data: $(form).serialize(),
        method: method,
        dataType: "json",
        beforeSend: function () {
            $(".page-loader").show();
        },
        error: (err) => {
            if (err.status === 403) {
                toastr.error(err.responseJSON.message);
                return;
            }
            if (err.status === 422) {
                showErrors(err.responseJSON.errors);
            }
            if (err.status === 428) { 
                toastr.error(err.responseJSON.message);
                return false;
            }
            toastr.error(err.responseJSON.message);
            showLoader();
            return false;
        },
        success: function (resp) {
            if (resp.success) {
                toastr.success(resp.success);
                $(formid).get(0).reset();
                $(popupid).find(".close").trigger("click");
                $(tableid).DataTable().draw();
            } else if (resp.errors) {
                toastr.error(resp.errors);
            }
            $(subbtn).attr("disabled", false);
        },
        complete: function () {
            $(".page-loader").hide();
        },
    });
}

function ajaxHandlercircular(
    form,
    url,
    method,
    formid,
    subbtn,
    popupid,
    tableid
) {
    showLoader();
    let formData = new FormData(form);
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('input[name="_token"]').attr("value"),
        },
    });
    $.ajax({
        url: url,
        data: formData,
        method: method,
        dataType: "json",
        contentType: false,
        processData: false,
        beforeSend: function () {
            $(".page-loader").show();
        },
        error: (err) => {
            if (err.status === 403) {
                toastr.error(err.responseJSON.message);
                return;
            }
            if (err.status === 422) {
                showErrors(err.responseJSON.errors);
            }
            toastr.error("Something went wrong!");
            showLoader();
            return false;
        },
        success: function (resp) {
            if (resp.success) {
                toastr.success(resp.success);
                $(formid).get(0).reset();
                $(popupid).find(".close").trigger("click");
                $(tableid).DataTable().draw();
            } else if (resp.errors) {
                toastr.error(resp.errors);
            }
            $(subbtn).attr("disabled", false);
        },
        complete: function () {
            $(".page-loader").hide();
        },
    });
}

//Ajax Validator
function showErrors(errors) {
    for (let key in errors) {
        if (!errors.hasOwnProperty(key)) continue;
        let inputName = buildName(key);
        let element = $("*[name='" + inputName + "']");

        element.on("input", clearError);
        // element.trigger("input");

        element.addClass("is-invalid");
        element.after(
            '<span class="error invalid-feedback">' + errors[key] + "</span>"
        );
    }
}

function buildName(key) {
    let name = "";
    let meta = key.split(".");
    meta.forEach(function (value, index) {
        name += value;
    });

    return name;
}

function clearError() {
    let input = $(this);
    input.next().remove();
    input.removeClass("is-invalid");
}

//Image Preview
$(".file-input").change(function () {
    let curElement = $(".image-product-logo");
    console.log(curElement);
    let reader = new FileReader();

    reader.onload = function (e) {
        curElement.attr("src", e.target.result);
    };

    reader.readAsDataURL(this.files[0]);
});

$(function () {
    $(document).tooltip();
});

$(document).ready(function () {
    $(".select2").select2();
    $('[data-toggle="tooltip"]').tooltip();

    $(".modal-dialog").draggable({
        handle: ".modal-header",
    });

    $(".notification").on("click", function (e) {
        e.stopPropagation();
    });

    $("#start_date").datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
        onSelect: function (dateStr) {
            $("#end_date").val(dateStr);
            $("#end_date").datepicker("option", { minDate: new Date(dateStr), maxDate:new Date(endYearDate) });
        },
    });
    $("#end_date").datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
    });

    $(".datepicker").datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
        yearRange: "-100:+10",
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
    });
});

//Multiple Approval
$("#request-select-all").on("click", function () {
    $(".recordselect").prop("checked", $(this).prop("checked"));
});

$(document).on("click", ".checkbox-m", function () {
    let id = [];

    $(".recordselect:checked").each(function () {
        id.push($(this).val());
    });

    if (id.length > 0) {
        $(".multipleapproval").prop("disabled", false);
        $(".multipledelete").prop("disabled", false);
    } else {
        $(".multipleapproval").prop("disabled", true);
        $(".multipledelete").prop("disabled", true);
    }
});

//Common functions
let doAjax_params_default = {
    url: null,
    requestType: "GET",
    data: {},
    successCallbackFunction: null,
};

function commonAjax(doAjax_params) {
    let url = doAjax_params["url"];
    let requestType = doAjax_params["requestType"];
    let data = doAjax_params["data"];
    let successCallbackFunction = doAjax_params["successCallbackFunction"];
    $.ajaxSetup({
        headers: {
            "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
        },
    });

    $.ajax({
        url: url,
        type: requestType,
        data: data,
        beforeSend: function () {
            $(".page-loader").show();
        },
        success: function (data) {
            if (typeof successCallbackFunction === "function") {
                successCallbackFunction(data);
            }
            $(".select2").select2();
        },
        complete: function () {
            $(".page-loader").hide();
        },
        error: (err) => {
            if (err.status === 403) {
                toastr.error(err.responseJSON.message);
                return;
            }
            if (err.status === 422) {
                showErrors(err.responseJSON.errors);
            }
            if(err.status === 428)
            {
                toastr.error(err.responseJSON.message);
                return false;
            }
            toastr.error("Something went wrong!");
            showLoader();
            return false;
        },
    });
}

function commonAlert(func) {
    Swal.fire({
        title: "Are you sure?",
        icon: "info",
        text: "You won't be able to revert this!",
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonText: "Yes, do it!",
        confirmButtonColor: "#000000 /* black theme */",
    }).then((result) => {
        if (result.isConfirmed) {
            if (typeof func === "function") {
                func();
            }
        } else {
            return false;
        }
    });
    return false;
}

function commonDatatable(tableid, url, columns, data, callBack) {
    return $(tableid).DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: url,
            data: data,
            dataSrc: function (res) {
                if (typeof callBack === "function") {
                    callBack(res);
                }
                return res.data;
            },
        },
        columns: columns,
        responsive: false,
        order: [],
        autoWidth: false,
        searching: true,
        scrollX: true,
        initComplete: function () {
            this.api()
                .columns(".sorting")
                .every(function () {
                    let column = this;
                    let title = column.header().textContent;
                    let inputHTML =
                        '<input type="text" class="table-search form-control" placeholder="' +
                        title +
                        '">';
                    column.footer().innerHTML = inputHTML;
                    let input = column.footer().querySelector("input");
                    input.addEventListener(
                        "keyup",
                        debounce(function () {
                            if (column.search() !== input.value) {
                                column.search(input.value).draw();
                            }
                        }, 300)
                    );
                });
        },
        drawCallback: function (settings) {
            $(`${tableid} tbody td`).each(function () {
                if (
                    !$(this).children().length &&
                    $(this).html().trim() === $(this).text().trim() &&
                    $(this).text().trim() !== "" 
                ) {
                    let cellData = $(this).text().trim();

                    let formattedHtml = `
                        <div class="fixed-column">
                            <span title="${cellData}">
                                ${cellData}
                            </span>
                        </div>`;

                    $(this).html(formattedHtml);
                }
                var footer = $(
                    `${tableid}_wrapper .table.dataTable tfoot .search-row`
                );
                $(`${tableid}`)
                    .closest(".dataTables_wrapper")
                    .find(".dataTables_scrollHead .table thead")
                    .append(footer);
            });
        },
    });
}

function debounce(func, delay) {
    let timeoutId;
    return function () {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            func.apply(this, arguments);
        }, delay);
    };
}

function exportData(url, data) {
    let params = $.extend({}, doAjax_params_default);
    params["url"] = url;
    params["requestType"] = `POST`;
    params["data"] = data;
    params["successCallbackFunction"] = function successCallbackFunction(
        result
    ) {
        if (result.success) {
            let a = document.createElement("a");
            a.href = result.file;
            a.download = result.name;
            document.body.appendChild(a);
            a.click();
            a.remove();
        } else {
            toastr.error(result.message);
        }
    };
    commonAjax(params);
}

$("#department_select").change(function () {
    $(".department-value").val($(this).val());

    let params = $.extend({}, doAjax_params_default);
    params["url"] = "users/ajax/list";
    params["data"] = {
        department: $(this).val(),
    };
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        response
    ) {
        let select = $(".users-data");
        select.empty();
        select.append($('<option value="">All</option>'));
        $.each(response.users, function (index, value) {
            select.append(
                $(
                    '<option value="' +
                        value.id +
                        '">' +
                        value.first_name + " " + value.first_name +
                        "</option>"
                )
            );
        });
    };
    commonAjax(params);
});

$("body").delegate(".timepicker", "focusin", function () {
    $(this).datetimepicker({
        format: "hh:mm A",
        icons: {
            up: "fa fa-chevron-up",
            down: "fa fa-chevron-down",
        },
    });
});

$(".timepicker").datetimepicker({
    format: "hh:mm A",
    icons: {
        up: "fa fa-chevron-up",
        down: "fa fa-chevron-down",
    },
});

$("body").delegate(".datepicker", "focusin", function () {
    $(this).datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
        minDate:new Date(startYearDate),
        maxDate:new Date(endYearDate),
        yearRange: "-100:+10",
    });
});

$("body").delegate(".datepicker_no_range", "focusin", function () {
    $(this).datepicker({
        dateFormat: "yy-mm-dd",
        changeMonth: true,
        changeYear: true,
        yearRange: "-100:+10",
    });
});

$(document).ready(function () {
    $(".modal-dialog").addClass("modal-dialog-centered");
});

function initializeCKEditor(targetElement) {
    ClassicEditor.create(targetElement, {
        ckfinder: {
            uploadUrl:
                window.location.origin + checkHost() + "/upload-file-api",
        },
    })
        .then((editor) => {
            window.editor = editor;
        })
        .catch((error) => {
            console.error(error);
        });
}

$(document).ready(function () {
    $(".filter-content").hide();
    $("#filters").click(function () {
        $(".filter-content").slideToggle()();
    });

    var menu = $(".nav-item:has(.nav-link.active)");

    if (menu.length > 0) {
        var hasChildren = menu.children("ul").length > 0;

        if (hasChildren) {
            menu.addClass("menu-is-opening menu-open");
            menu.children("ul").css("display", "block");
        }
    }
});

function checkHost() {
    return "";
}

$(document).on("change", "#department-transfer", function () {
    let params = $.extend({}, doAjax_params_default);
    params["url"] =
        window.location.origin + checkHost() + "/ajax/classroom";
    params["requestType"] = `GET`;
    params["data"] = {
        department: $(this).val(),
        year: $("#year-transfer").val(),
    };
    params["successCallbackFunction"] = function successCallbackFunction(
        response
    ) {
        let select = $(".classroom-transfer-data");
        select.empty();
        select.append($('<option value="">Select Classroom</option>'));
        $.each(response.classlist, function (index, value) {
            select.append(
                $(
                    '<option value="' +
                        value.id +
                        '">' +
                        value.class_name +
                        "</option>"
                )
            );
        });
    };
    commonAjax(params);
});

$("#yearChange").change(function () {
    let params = $.extend({}, doAjax_params_default);
    params["url"] =
        window.location.origin +
        checkHost() +
        "/change-year/" +
        $(this).val();
    params["requestType"] = `GET`;
    params["successCallbackFunction"] = function successCallbackFunction(
        response
    ) {
        toastr.success(response.success);
        setTimeout(() => {
            location.reload();
        }, 1000);
    };
    commonAjax(params);
});

$(document).ready(function () {
    $("#shift_start_time").datetimepicker({
        format: "HH:mm:00",
        icons: {
            up: "fa fa-chevron-up",
            down: "fa fa-chevron-down",
        },
    });
    $("#shift_end_time").datetimepicker({
        format: "HH:mm:00",
        icons: {
            up: "fa fa-chevron-up",
            down: "fa fa-chevron-down",
        },
    });
    $("#shift_half_time").datetimepicker({
        format: "HH:mm:00",
        icons: {
            up: "fa fa-chevron-up",
            down: "fa fa-chevron-down",
        },
    });
});

const toggleSwitch = document.querySelector("#darkmode-toggle");
const currentTheme = localStorage.getItem("darkMode");
const body = document.body;

if (currentTheme == "enabled") {
    body.classList.add("dark-mode");
    toggleSwitch.checked = false;
} else {
    body.classList.remove("dark-mode");
    toggleSwitch.checked = true;
}

function switchTheme(e) {
    if (e.target.checked) {
        body.classList.remove("dark-mode");
        localStorage.setItem("darkMode", "disabled");
    } else {
        body.classList.add("dark-mode");
        localStorage.setItem("darkMode", "enabled");
    }
}

toggleSwitch.addEventListener("change", switchTheme, false);

let columns = [
    {
        data: "action",
        name: "action",
        orderable: false,
    },
    {
        data: "student_full_name",
        name: "student_full_name",
    },
    {
        data: "contact_no",
        name: "contact_no",
    },
    {
        data: "class_name",
        name: "class_name",
    },
    {
        data: "department_name",
        name: "department_name",
    },
];

let studentdata = function (d) {
    d.department = $("#department_global_search").val();
    d.classroom = $("#classroom_global_search").val();
    d.search_key = $("#search_global").val();
};

let tableSearch = commonDatatable(
    "#studentSearchTable",
    searchGlobalStudent,
    columns,
    studentdata
);

$("#openStudentGlobalModal").on("click", function (event) {
    event.preventDefault();
    tableSearch.draw();
});

$(".openStudent").click(function(e) {
    e.preventDefault();
    $("#studentCount").modal('hide');
    var classroomID = $(this).attr("data-classroomid");
    var departmentID = $(this).attr("data-departmentid");

    $('#department_global_search').val(departmentID).trigger('change');

    setTimeout(function() {
        $('#classroom_global_search').val(classroomID).trigger('change');
        $("#openStudentGlobalModal").trigger("click");
    }, 1000);
});

$(document).ready(function () {
    $('#sidebarSearch').on('input', function () {
      const filter = $(this).val().toLowerCase();
  
      $('#sidebarItems .col-3').each(function () {
        const text = $(this).find('p').text().toLowerCase();
  
        if (text.includes(filter)) {
          $(this).show();
        } else {
          $(this).hide();
        }
      });
    });
  });