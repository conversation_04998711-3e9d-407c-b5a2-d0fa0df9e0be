"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Student } from "@/lib/types";
import { ColumnDef } from "@tanstack/react-table";
import { Loader2, Eye, Edit, Download, Trash2 } from "lucide-react";
import {
  getAllStudentCounts,
  getStudents,
  downloadStudentsExcel,
  deleteStudent,
} from "@/services/studentApi";
import { DataTable } from "@/app-components/dataTable";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";

import Pagination from "@/app-components/pagination";
import ConfirmDialog from "@/app-components/ConfirmDialog";

const StudentDetailsPage = () => {
  const router = useRouter();
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleting, setIsDeleting] = useState(false);
  const [filters, setFilters] = useState<{
    name?: string;
    email?: string;
    contact?: string;
    status?: string;
  }>({});
  const [appliedFilters, setAppliedFilters] = useState<{
    name?: string;
    email?: string;
    contact?: string;
    status?: string;
  }>({});
  const [counts, setCounts] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    totalCoins: 0,
  });
  const [countsLoading, setCountsLoading] = useState(true);
  const [deleteStudentId, setDeleteStudentId] = useState<string | null>(null);
  const PAGE_SIZE = 10;

  const fetchStudents = useCallback(
    async (page: number = 1, appliedFilters = {}) => {
      try {
        setIsLoading(true);
        const data = await getStudents(page, PAGE_SIZE, appliedFilters);
        setStudents(data.students);
        setTotalPages(data.totalPages);
        setCurrentPage(data.page);
      } catch (error) {
        console.error("Failed to fetch students:", error);
        setStudents([]);
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const fetchCounts = useCallback(async () => {
    try {
      setCountsLoading(true);
      const data = await getAllStudentCounts();
      if (data.success === false) {
        throw new Error(data.error);
      }
      setCounts({
        total: data.total || 0,
        pending: data.pending || 0,
        approved: data.approved || 0,
        rejected: data.rejected || 0,
        totalCoins: data.totalCoins || 0,
      });
    } catch (error) {
      console.error("Failed to fetch counts:", error);
    } finally {
      setCountsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStudents(1, {});
    fetchCounts();
  }, [fetchStudents, fetchCounts]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchStudents(page, appliedFilters);
  };

  const handleViewProfile = (student: Student) => {
    router.push(`/student-profile/${student.id}`);
  };

  const handleEditProfile = (student: Student) => {
    router.push(`/student-edit/${student.id}`);
  };

  const handleDeleteStudent = (student: Student) => {
    setDeleteStudentId(student.id);
  };

  const confirmDeleteStudent = async () => {
    if (!deleteStudentId) return;
    setIsDeleting(true);
    try {
      await deleteStudent(deleteStudentId);
      toast.success("Student deleted successfully");
      fetchStudents(currentPage, appliedFilters);
      fetchCounts();
    } catch (error: any) {
      const errorMessage = error.message || "Failed to delete student";
      console.error(`Error deleting student ${deleteStudentId}:`, errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
      setDeleteStudentId(null);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value || undefined,
    }));
  };

  const handleSearch = () => {
    setCurrentPage(1);
    setAppliedFilters(filters);
    fetchStudents(1, filters);
  };

  const handleReset = () => {
    setFilters({});
    setAppliedFilters({});
    setCurrentPage(1);
    fetchStudents(1, {});
  };

  const handleStatusChange = (value: string) => {
    setFilters((prev) => ({
      ...prev,
      status: value || undefined,
    }));
  };

  const handleDownloadExcel = async () => {
    try {
      await downloadStudentsExcel(appliedFilters);
      toast.success("Excel file downloaded successfully");
    } catch (error) {
      toast.error("Failed to download Excel file");
      console.error("Download error:", error);
    }
  };

  const columns: ColumnDef<Student>[] = [
    {
      accessorKey: "firstName",
      header: "First Name",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "contact",
      header: "Contact Number",
    },
    {
      accessorKey: "coins",
      header: "Uest Coins",
    },
    {
      accessorKey: "isVerified",
      header: "Verification Status",
      cell: ({ row }) => (
        <Badge
          className={
            row.original.isVerified
              ? "bg-green-200 text-green-800"
              : "bg-red-200 text-red-800"
          }
        >
          {row.original.isVerified ? "Verified" : "Not Verified"}
        </Badge>
      ),
    },
    {
      accessorKey: "profile.status",
      header: "Profile Status",
      cell: ({ row }) => {
        const status = row.original.profile?.status;
        if (!status)
          return <span className="text-gray-400 text-sm">No Profile</span>;

        const getStatusClass = (status: string) => {
          switch (status) {
            case "APPROVED":
              return "bg-green-200 text-green-800";
            case "REJECTED":
              return "bg-red-200 text-red-800";
            default:
              return "bg-yellow-200 text-yellow-800";
          }
        };

        return <Badge className={getStatusClass(status)}>{status}</Badge>;
      },
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return date.toLocaleDateString();
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleViewProfile(row.original)}
            className="hover:bg-gray-100"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleEditProfile(row.original)}
            className="hover:bg-gray-100"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDeleteStudent(row.original)}
            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100"
            disabled={row.original.profile?.status === "APPROVED"}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Students Details</h1>
      </div>

      {/* Students Card */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-6 mb-6">
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Total Students
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {countsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                counts.total
              )}
            </CardDescription>
          </CardContent>
        </Card>
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Pending Profiles
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {countsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                counts.pending
              )}
            </CardDescription>
          </CardContent>
        </Card>
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Approved Profiles
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {countsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                counts.approved
              )}
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Rejected Profiles
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {countsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                counts.rejected
              )}
            </CardDescription>
          </CardContent>
        </Card>
        <Card className="bg-white rounded-xl shadow-md">
          <CardContent className="flex flex-col justify-center h-24 px-5">
            <div className="flex items-center justify-center">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-center font-medium text-gray-700 tracking-wide">
                  Total Coins
                </CardTitle>
              </div>
            </div>
            <CardDescription className="text-xl font-semibold text-black text-center">
              {countsLoading ? (
                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
              ) : (
                counts.totalCoins
              )}
            </CardDescription>
          </CardContent>
        </Card>
      </div>
      <hr />
      {/* End of Students Card */}

      <div className="my-6 flex flex-col sm:flex-row gap-4">
        <Input
          name="name"
          placeholder="Search by name..."
          value={filters.name || ""}
          onChange={handleFilterChange}
          className="max-w-xs"
        />
        <Input
          name="email"
          placeholder="Search by email..."
          value={filters.email || ""}
          onChange={handleFilterChange}
          className="max-w-xs"
        />
        <Input
          name="contact"
          placeholder="Search by contact..."
          value={filters.contact || ""}
          onChange={handleFilterChange}
          className="max-w-xs"
        />

        <Select
          name="status"
          value={filters.status || ""}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger className="max-w-xs">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>

        <div className="mt-2 flex items-center space-x-3 me-3">
          <Button onClick={handleSearch}>Search</Button>
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
          <Button
            variant="outline"
            onClick={handleDownloadExcel}
            className="flex items-center gap-2 text-white hover:text-white bg-[#ff914d] hover:bg-[#ff914d]"
          >
            <Download className="h-4 w-4  text-white  " />
            Download xlsx
          </Button>
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      <DataTable
        columns={columns}
        data={students}
        isLoading={isLoading}
      />

      <Pagination
        page={currentPage}
        totalPages={totalPages}
        setPage={handlePageChange}
        entriesText={`${students.length} entries`}
      />

      <ConfirmDialog
        open={!!deleteStudentId}
        setOpen={(val) => {
          if (!val) setDeleteStudentId(null);
        }}
        title="Are you sure?"
        description="This action cannot be undone. This will permanently delete the student and all associated data."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDeleteStudent}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default StudentDetailsPage;
