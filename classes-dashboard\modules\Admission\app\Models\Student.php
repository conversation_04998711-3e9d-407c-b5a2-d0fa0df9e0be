<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Student extends Model
{
    protected $table = 'Student';

    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;
    public $timestamps = false;

    protected $fillable = [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'mothersName',
        'email',
        'contact',
        'googleId',
        'profilePhoto',
        'isVerified',
        'updatedAt',
        'createdAt',
    ];

    public function getAcademicInfo()
    {
        return $this->hasOne(StudentAcademicInfo::class, 'student_id', 'id')->withDefault();
    }

    public function getStudentDetails()
    {
        return $this->hasOne(StudentDetails::class, 'studentId', 'id')->withDefault();
    }

    protected static function boot()
    {
        parent::boot();

        // Generate UUID on creation only
        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = Str::uuid()->toString();
            }
        });
    }
}

