'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { verifyJWT } from '@/lib/helper';

const useStudentId = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [studentId, setStudentId] = useState<string | null>(null);

  useEffect(() => {
    const init = async () => {
      const token = searchParams.get('token');

      if (token) {
        const payload = await verifyJWT(token);
        if (payload?.id) {
          const studentData = { id: payload.id };
          localStorage.setItem('student_data', JSON.stringify(studentData));
          setStudentId(payload.id);

          const newUrl = window.location.pathname;
          router.replace(newUrl);
        }
      } else {
        const local = localStorage.getItem('student_data');
        const parsed = local ? JSON.parse(local) : null;
        setStudentId(parsed?.id || null);
      }
    };

    init();
  }, [searchParams]);

  return studentId;
};

export default useStudentId;