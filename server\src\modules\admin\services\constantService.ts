import prisma from '@/config/prismaClient';
import { 
  CreateCategoryRequest, 
  UpdateCategoryRequest,
  CreateDetailRequest,
  UpdateDetailRequest,
  CreateSubDetailRequest,
  UpdateSubDetailRequest,
  CreateValueRequest,
  UpdateValueRequest,

} from '../requests/constantRequest';

// ConstantCategory Services
export const createCategory = async (data: CreateCategoryRequest) => {
  return await prisma.constantCategory.create({
    data,
    include: {
      details: {
        include: {
          subDetails: {
            include: {
              values: true
            }
          }
        }
      }
    }
  });
};

export const getAllCategories = async (page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;

  const whereClause = search ? {
    name: {
      contains: search,
      mode: 'insensitive' as const
    }
  } : {};

  const [categories, total] = await Promise.all([
    prisma.constantCategory.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: {
        details: {
          include: {
            subDetails: {
              include: {
                values: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.constantCategory.count({
      where: whereClause
    })
  ]);

  return {
    data: categories,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
};

export const getCategoryById = async (id: string) => {
  return await prisma.constantCategory.findUnique({
    where: { id },
    include: {
      details: {
        include: {
          subDetails: {
            include: {
              values: true
            }
          }
        }
      }
    }
  });
};

export const updateCategory = async (id: string, data: UpdateCategoryRequest) => {
  return await prisma.constantCategory.update({
    where: { id },
    data,
    include: {
      details: {
        include: {
          subDetails: {
            include: {
              values: true
            }
          }
        }
      }
    }
  });
};

export const deleteCategory = async (id: string) => {
  return await prisma.constantCategory.delete({
    where: { id }
  });
};

// ConstantDetail Services
export const createDetail = async (data: CreateDetailRequest) => {
  return await prisma.constantDetail.create({
    data,
    include: {
      category: true,
      subDetails: {
        include: {
          values: true
        }
      }
    }
  });
};

export const getDetailsByCategory = async (categoryId: string, page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;

  const whereClause = {
    categoryId,
    ...(search && {
      name: {
        contains: search,
        mode: 'insensitive' as const
      }
    })
  };

  const [details, total] = await Promise.all([
    prisma.constantDetail.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: {
        category: true,
        subDetails: {
          include: {
            values: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.constantDetail.count({
      where: whereClause
    })
  ]);

  return {
    data: details,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
};

export const getDetailById = async (id: string) => {
  return await prisma.constantDetail.findUnique({
    where: { id },
    include: {
      category: true,
      subDetails: {
        include: {
          values: true
        }
      }
    }
  });
};

export const updateDetail = async (id: string, data: UpdateDetailRequest) => {
  return await prisma.constantDetail.update({
    where: { id },
    data,
    include: {
      category: true,
      subDetails: {
        include: {
          values: true
        }
      }
    }
  });
};

export const deleteDetail = async (id: string) => {
  return await prisma.constantDetail.delete({
    where: { id }
  });
};

// ConstantSubDetail Services
export const createSubDetail = async (data: CreateSubDetailRequest) => {
  return await prisma.constantSubDetail.create({
    data,
    include: {
      detail: {
        include: {
          category: true
        }
      },
      values: true
    }
  });
};

export const getSubDetailsByDetail = async (detailId: string, page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;

  const whereClause = {
    detailId,
    ...(search && {
      name: {
        contains: search,
        mode: 'insensitive' as const
      }
    })
  };

  const [subDetails, total] = await Promise.all([
    prisma.constantSubDetail.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: {
        detail: {
          include: {
            category: true
          }
        },
        values: true
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.constantSubDetail.count({
      where: whereClause
    })
  ]);

  return {
    data: subDetails,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
};

export const getSubDetailById = async (id: string) => {
  return await prisma.constantSubDetail.findUnique({
    where: { id },
    include: {
      detail: {
        include: {
          category: true
        }
      },
      values: true
    }
  });
};

export const updateSubDetail = async (id: string, data: UpdateSubDetailRequest) => {
  return await prisma.constantSubDetail.update({
    where: { id },
    data,
    include: {
      detail: {
        include: {
          category: true
        }
      },
      values: true
    }
  });
};

export const deleteSubDetail = async (id: string) => {
  return await prisma.constantSubDetail.delete({
    where: { id }
  });
};

// ConstantSubDetailValue Services
export const createValue = async (data: CreateValueRequest) => {
  return await prisma.constantSubDetailValue.create({
    data,
    include: {
      subDetail: {
        include: {
          detail: {
            include: {
              category: true
            }
          }
        }
      }
    }
  });
};

export const getValuesBySubDetail = async (subDetailId: string, page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;

  const whereClause = {
    subDetailId,
    ...(search && {
      name: {
        contains: search,
        mode: 'insensitive' as const
      }
    })
  };

  const [values, total] = await Promise.all([
    prisma.constantSubDetailValue.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: {
        subDetail: {
          include: {
            detail: {
              include: {
                category: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    }),
    prisma.constantSubDetailValue.count({
      where: whereClause
    })
  ]);

  return {
    data: values,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
};

export const getValueById = async (id: string) => {
  return await prisma.constantSubDetailValue.findUnique({
    where: { id },
    include: {
      subDetail: {
        include: {
          detail: {
            include: {
              category: true
            }
          }
        }
      }
    }
  });
};

export const updateValue = async (id: string, data: UpdateValueRequest) => {
  return await prisma.constantSubDetailValue.update({
    where: { id },
    data,
    include: {
      subDetail: {
        include: {
          detail: {
            include: {
              category: true
            }
          }
        }
      }
    }
  });
};

export const deleteValue = async (id: string) => {
  return await prisma.constantSubDetailValue.delete({
    where: { id }
  });
};


