<?php

namespace ClassWork\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use ClassWork\Http\Requests\CreateClassWorkRequest;
use ClassWork\Repositories\ClassWorkRepository;
use Classroom\Repositories\ClassroomRepository;
use Subject\Repositories\SubjectRepository;

class ClassWorkController extends Controller
{
    protected $classWorkRepository;
    protected $classroomRepository;
    protected $subjectRepository;

    public function __construct(ClassWorkRepository $classWorkRepository, ClassroomRepository $classroomRepository, SubjectRepository $subjectRepository)
    {
        $this->middleware('permission:read classWork', ['only' => ['index']]);
        $this->middleware('permission:create classWork', ['only' => ['create', 'store']]);
        $this->middleware('permission:delete classWork', ['only' => ['delete','destroy']]);
        $this->middleware('permission:update classWork', ['only' => ['edit', 'update']]);
        $this->classWorkRepository = $classWorkRepository;
        $this->classroomRepository = $classroomRepository;
        $this->subjectRepository = $subjectRepository;
    }
   
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $list = $this->classWorkRepository->getAll($request);
            return $this->classWorkRepository->getDatatable($list);
            dd($list);
        }
      return view('ClassWork::index');
    }

    public function create(Request $request)
    {
        $classroom = $this->classroomRepository->getAll($request)->get();
        $subject = $this->subjectRepository->getAll($request)->get();
        return view('ClassWork::create', compact('classroom', 'subject'));
    }

    public function store(CreateClassWorkRequest $request)
    {
        $this->classWorkRepository->storeClassWork($request);
        return response()->json(['success' => 'Classwork Added Successfully!!']);
    }

    public function edit($id, Request $request)
    {
        $data = $this->classWorkRepository->getClassWorkById($id);
        $classroom = $this->classroomRepository->getAll($request)->get();
        $subject = $this->subjectRepository->getAll($request)->get();
        return view('ClassWork::edit', compact('data', 'classroom', 'subject'));
    }

    public function update(CreateClassWorkRequest $request, $id)
    {
        $this->classWorkRepository->updateClassWork($request, $id);
        return response()->json(['success' => 'Classwork Updated Successfully!!']);
    }

    public function destroy($id)
    {
        $job = $this->classWorkRepository->getClassWorkById($id);
        $job->delete($id);
        return response()->json(['success' => 'Classwork deleted successfully!!']);
    }

    public function getAllClassWorks()
    {
        $data = $this->classWorkRepository->getAllClassWorks();
        return response()->json(['success' => 'Classwork fetched successfully!!', 'data' => $data]);
    }

    public function getClassWorkById($id)
    {
        $data = $this->classWorkRepository->getClassWorkById($id);
        return response()->json(['success' => 'Classwork fetched successfully!!', 'data' => $data]);
    }
}
