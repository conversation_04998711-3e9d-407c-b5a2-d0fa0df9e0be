@extends('layouts.app')
@section('content')

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-9">
                <h1>Class Work </h1>
            </div>
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">

                <div class="card">
                    <!-- /.card-header -->
                    <div class="card-body">
                        <div class="generate-buttons">
                            @can('create classWork')
                                <a id="addClassWorkEntry" data-toggle="modal" data-target="#newClassWorkEntry" class="btn btn-primary"><i class="fa fa-plus-square"></i>&nbsp; Add New Class Work</a>
                            </a>
                            @endcan
                        </div>
                        <table id="classWork_table" class="table display  table-striped  table-borderless dt-responsive">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Date</th>
                                    <th>Classroom</th>
                                    <th>Subject</th>
                                    <th>Title</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr class="search-row">
                                    <th>Action</th>
                                    <th>Date</th>
                                    <th>Classroom</th>
                                    <th>Subject</th>
                                    <th>Title</th>
                                    <th>Description</th>
                                </tr>
                            </tfoot>
                        </table>
                        <div class="modal" id="newClassWorkEntry" tabindex="-1" role="dialog" aria-labelledby="roleModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg salary-o-popup" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h3 id="modeltitle" class="box-title popup-title m-0">Add New Entry</h3>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div id="createContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection
@section('scripts')
<script>
     var classWorksRoute = {
        index: "{{ route('classWork.index') }}",
        create: "{{ route('classWork.create') }}",
        edit: "{{ route('classWork.edit',':editdid') }}",
        store: "{{ route('classWork.store') }}",
        delete: "{{ route('classWork.destroy',':classWorkid') }}",
    };
</script>
<script src="{{ asset(mix('js/page-level-js/ClassWork/index.js')) }}"></script>
<script src="{{ asset('assets/js/ckeditor.js') }}"></script>

@endsection